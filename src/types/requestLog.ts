// 请求日志相关类型定义

// 请求日志查询参数
export interface RequestLogQueryRequest {
  page: number;
  size: number;
  userId?: number;
  provider?: string;
  status?: string;
  requestType?: string;
  modelName?: string;
  startTime?: string;
  endTime?: string;
  onlyFailed?: boolean;
  onlySlow?: boolean;
  minDuration?: number;
  maxDuration?: number;
  retryCountRange?: { min: number; max: number };
}

// 请求日志响应
export interface OpenAiRequestLogResponse {
  id: number;
  requestId: string;
  userId?: number;
  requestType: string;
  modelName: string;
  provider: string;
  groupName?: string;
  maskedApiKey?: string;
  actualBaseUrl?: string;
  pathSuffix?: string;
  status: string;
  statusCode?: number;
  errorMessage?: string;
  startTime: string;
  endTime?: string;
  durationMs?: number;
  retryCount: number;
  createdAt: string;
}

// 请求日志详情
export interface RequestLogDetail extends OpenAiRequestLogResponse {
  requestHeaders?: Record<string, string>;
  requestBody?: any;
  responseHeaders?: Record<string, string>;
  responseBody?: any;
  errorDetails?: {
    type: string;
    message: string;
    stack?: string;
  };
}

// 日志统计数据
export interface LogStatsData {
  totalRequests: number;
  successRequests: number;
  failedRequests: number;
  successRate: number;
  avgDuration: number;
  maxDuration: number;
  minDuration: number;
  totalDuration: number;
}

// 提供商统计
export interface ProviderStats {
  provider: string;
  requestCount: number;
  successCount: number;
  failedCount: number;
  avgDuration: number;
  successRate: number;
}

// 时间段统计
export interface TimeRangeStats {
  timeRange: string;
  requestCount: number;
  successCount: number;
  failedCount: number;
  avgDuration: number;
}

// 错误类型统计
export interface ErrorTypeStats {
  errorType: string;
  count: number;
  percentage: number;
  examples: string[];
}

// 模型使用统计
export interface ModelUsageStats {
  modelName: string;
  requestCount: number;
  successCount: number;
  failedCount: number;
  avgDuration: number;
  totalTokens?: number;
}

// 日志搜索表单
export interface LogSearchForm {
  dateRange: string[];
  provider: string;
  status: string;
  requestType: string;
  modelName: string;
  userId?: number;
  onlyFailed: boolean;
  onlySlow: boolean;
  minDuration?: number;
}

// 日志导出选项
export interface LogExportOptions {
  format: 'csv' | 'json' | 'excel';
  fields: string[];
  filters: RequestLogQueryRequest;
  includeHeaders: boolean;
  maxRecords?: number;
}

// 日志分析结果
export interface LogAnalysisResult {
  summary: LogStatsData;
  providerStats: ProviderStats[];
  timeRangeStats: TimeRangeStats[];
  errorTypeStats: ErrorTypeStats[];
  modelUsageStats: ModelUsageStats[];
  trends: {
    requestTrend: Array<{ time: string; count: number }>;
    errorTrend: Array<{ time: string; count: number }>;
    durationTrend: Array<{ time: string; avgDuration: number }>;
  };
}

// 分页结果
export interface PageResult<T> {
  data: T[];
  total: number;
  page: number;
  size: number;
  totalPages: number;
}

// API响应基础结构
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}
