import { defineStore } from "pinia";
import { ref, reactive, computed } from "vue";
import { RequestLogApi } from "@/api/requestLog";
import type {
  RequestLogQueryRequest,
  OpenAiRequestLogResponse,
  RequestLogDetail,
  LogStatsData,
  ProviderStats,
  TimeRangeStats,
  ErrorTypeStats,
  ModelUsageStats,
  LogAnalysisResult,
  PageResult
} from "@/types/requestLog";

export const useRequestLogStore = defineStore("requestLog", () => {
  // 日志数据
  const logs = ref<OpenAiRequestLogResponse[]>([]);
  const currentLog = ref<RequestLogDetail | null>(null);

  // 分页信息
  const pagination = reactive({
    page: 1,
    size: 20,
    total: 0,
    totalPages: 0
  });

  // 查询条件
  const filters = ref<RequestLogQueryRequest>({
    page: 1,
    size: 20
  });

  // 统计数据
  const stats = ref<LogStatsData | null>(null);
  const providerStats = ref<ProviderStats[]>([]);
  const timeRangeStats = ref<TimeRangeStats[]>([]);
  const errorTypeStats = ref<ErrorTypeStats[]>([]);
  const modelUsageStats = ref<ModelUsageStats[]>([]);
  const analysisResult = ref<LogAnalysisResult | null>(null);

  // 加载状态
  const loading = reactive({
    logs: false,
    detail: false,
    stats: false,
    analysis: false,
    export: false
  });

  // 错误信息
  const error = ref<string | null>(null);

  // 缓存控制
  const lastFetchTime = reactive({
    logs: 0,
    stats: 0,
    analysis: 0
  });

  // Computed properties (替代 getters)
  // 获取成功的请求数量
  const successLogsCount = computed(() => {
    return logs.value.filter(log => log.status === "success").length;
  });

  // 获取失败的请求数量
  const failedLogsCount = computed(() => {
    return logs.value.filter(log => log.status === "failed").length;
  });

  // 计算成功率
  const successRate = computed(() => {
    if (logs.value.length === 0) return 0;
    const successCount = logs.value.filter(log => log.status === "success").length;
    return Math.round((successCount / logs.value.length) * 100);
  });

  // 计算平均响应时间
  const avgResponseTime = computed(() => {
    const validLogs = logs.value.filter(log => log.durationMs && log.durationMs > 0);
    if (validLogs.length === 0) return 0;
    const totalTime = validLogs.reduce((sum, log) => sum + (log.durationMs || 0), 0);
    return Math.round(totalTime / validLogs.length);
  });

  // 按提供商分组的日志
  const logsByProvider = computed(() => {
    return logs.value.reduce((acc, log) => {
      if (!acc[log.provider]) {
        acc[log.provider] = [];
      }
      acc[log.provider].push(log);
      return acc;
    }, {} as Record<string, OpenAiRequestLogResponse[]>);
  });

  // 获取慢请求（超过5秒）
  const slowLogs = computed(() => {
    return logs.value.filter(log => log.durationMs && log.durationMs > 5000);
  });

  // 获取重试次数较多的请求
  const highRetryLogs = computed(() => {
    return logs.value.filter(log => log.retryCount > 2);
  });

  // Actions (函数方法)
  // 基础查询操作
  const fetchLogs = async (query?: Partial<RequestLogQueryRequest>) => {
    loading.logs = true;
    error.value = null;

    try {
      const finalQuery = { ...filters.value, ...query };
      filters.value = finalQuery;

      const result = await RequestLogApi.getOpenaiRequestLogsPage(finalQuery);

      logs.value = result.data || [];
      pagination.page = result.page || 1;
      pagination.size = result.size || 20;
      pagination.total = result.total || 0;
      pagination.totalPages = result.totalPages || 0;
      lastFetchTime.logs = Date.now();

      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "获取日志失败";
      // 确保在错误时logs仍然是数组
      if (!Array.isArray(logs.value)) {
        logs.value = [];
      }
      throw err;
    } finally {
      loading.logs = false;
    }
  };

  const fetchFailedLogs = async (page = 1, size = 20, userId?: number) => {
    loading.logs = true;
    error.value = null;

    try {
      const result = await RequestLogApi.getFailedOpenaiRequestLogs(page, size, userId);
      logs.value = result.data;
      pagination.page = result.page;
      pagination.size = result.size;
      pagination.total = result.total;
      pagination.totalPages = result.totalPages;
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "获取失败日志失败";
      throw err;
    } finally {
      loading.logs = false;
    }
  };

  const fetchLogsByProvider = async (provider: string, page = 1, size = 20) => {
    loading.logs = true;
    error.value = null;

    try {
      const result = await RequestLogApi.getOpenaiRequestLogsByProvider(provider, page, size);
      logs.value = result.data;
      pagination.page = result.page;
      pagination.size = result.size;
      pagination.total = result.total;
      pagination.totalPages = result.totalPages;
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "获取提供商日志失败";
      throw err;
    } finally {
      loading.logs = false;
    }
  };

  const fetchSlowLogs = async (page = 1, size = 20, minDuration = 5000, userId?: number) => {
    loading.logs = true;
    error.value = null;

    try {
      const result = await RequestLogApi.getSlowOpenaiRequestLogs(page, size, minDuration, userId);
      logs.value = result.data;
      pagination.page = result.page;
      pagination.size = result.size;
      pagination.total = result.total;
      pagination.totalPages = result.totalPages;
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "获取慢请求日志失败";
      throw err;
    } finally {
      loading.logs = false;
    }
  };

  const fetchLogDetail = async (requestId: string) => {
    loading.detail = true;
    error.value = null;

    try {
      currentLog.value = await RequestLogApi.getRequestLogDetail(requestId);
      return currentLog.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "获取日志详情失败";
      throw err;
    } finally {
      loading.detail = false;
    }
  };

  // 删除操作
  const deleteLog = async (id: number) => {
    loading.logs = true;
    error.value = null;

    try {
      await RequestLogApi.deleteOpenaiRequestLog(id);
      logs.value = logs.value.filter(log => log.id !== id);
      pagination.total -= 1;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "删除日志失败";
      throw err;
    } finally {
      loading.logs = false;
    }
  };

  // 统计数据
  const fetchStats = async (startTime?: string, endTime?: string, provider?: string) => {
    loading.stats = true;
    error.value = null;

    try {
      stats.value = await RequestLogApi.getLogStats(startTime, endTime, provider);
      lastFetchTime.stats = Date.now();
      return stats.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "获取统计数据失败";
      throw err;
    } finally {
      loading.stats = false;
    }
  };

  const fetchProviderStats = async (startTime?: string, endTime?: string) => {
    loading.stats = true;
    error.value = null;

    try {
      providerStats.value = await RequestLogApi.getProviderStats(startTime, endTime);
      return providerStats.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "获取提供商统计失败";
      throw err;
    } finally {
      loading.stats = false;
    }
  };

  // 更新过滤条件
  const updateFilters = (newFilters: Partial<RequestLogQueryRequest>) => {
    filters.value = { ...filters.value, ...newFilters };
  };

  // 重置过滤条件
  const resetFilters = () => {
    filters.value = {
      page: 1,
      size: 20
    };
  };

  // 清除错误
  const clearError = () => {
    error.value = null;
  };

  // 重置状态
  const resetState = () => {
    logs.value = [];
    currentLog.value = null;
    pagination.page = 1;
    pagination.size = 20;
    pagination.total = 0;
    pagination.totalPages = 0;
    filters.value = { page: 1, size: 20 };
    stats.value = null;
    providerStats.value = [];
    timeRangeStats.value = [];
    errorTypeStats.value = [];
    modelUsageStats.value = [];
    analysisResult.value = null;
    loading.logs = false;
    loading.detail = false;
    loading.stats = false;
    loading.analysis = false;
    loading.export = false;
    error.value = null;
    lastFetchTime.logs = 0;
    lastFetchTime.stats = 0;
    lastFetchTime.analysis = 0;
  };

  // 返回所有需要暴露的状态和方法
  return {
    // 状态
    logs,
    currentLog,
    pagination,
    filters,
    stats,
    providerStats,
    timeRangeStats,
    errorTypeStats,
    modelUsageStats,
    analysisResult,
    loading,
    error,
    lastFetchTime,

    // 计算属性
    successLogsCount,
    failedLogsCount,
    successRate,
    avgResponseTime,
    logsByProvider,
    slowLogs,
    highRetryLogs,

    // 方法
    fetchLogs,
    fetchFailedLogs,
    fetchLogsByProvider,
    fetchLogDetail,
    deleteLog,
    fetchStats,
    fetchProviderStats,
    updateFilters,
    resetFilters,
    clearError,
    resetState
  };
});
