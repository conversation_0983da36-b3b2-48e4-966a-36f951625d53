import { defineStore } from "pinia";
import { ref, reactive, computed } from "vue";
import { AiConfigApi } from "@/api/aiConfig";
import type {
  UserConfigGroupDto,
  ApiKeyDto,
  BatchAddApiKeyRequest,
  BatchAddApiKeyResult,
  CompatibleApiKeyDto,
  CreateCompatibleKeyRequest,
  ApiKeyTestResult,
  ApiKeyUpdateRequest,
  ApiKeyStatsDto
} from "@/types/aiConfig";

export const useAiConfigStore = defineStore("aiConfig", () => {
  // 配置分组相关
  const configGroups = ref<UserConfigGroupDto[]>([]);
  const currentGroup = ref<UserConfigGroupDto | null>(null);

  // API密钥相关
  const apiKeys = ref<ApiKeyDto[]>([]);
  const currentApiKey = ref<ApiKeyDto | null>(null);

  // 兼容密钥相关
  const compatibleKeys = ref<CompatibleApiKeyDto[]>([]);

  // 统计数据
  const keyStats = ref<ApiKeyStatsDto | null>(null);

  // 加载状态
  const loading = reactive({
    groups: false,
    keys: false,
    compatibleKeys: false,
    stats: false,
    testing: false
  });

  // 错误信息
  const error = ref<string | null>(null);

  // 缓存控制
  const lastFetchTime = reactive({
    groups: 0,
    keys: 0,
    compatibleKeys: 0,
    stats: 0
  });

  // Computed properties (替代 getters)
  // 获取当前分组的API密钥数量
  const currentGroupApiKeyCount = computed(() => {
    return currentGroup.value ? apiKeys.value.length : 0;
  });

  // 获取活跃的API密钥数量
  const activeApiKeyCount = computed(() => {
    return apiKeys.value.filter(key => key.isActive).length;
  });

  // 获取失效的API密钥数量
  const inactiveApiKeyCount = computed(() => {
    return apiKeys.value.filter(key => !key.isActive).length;
  });

  // 按提供商分组的配置分组
  const groupsByProvider = computed(() => {
    return configGroups.value.reduce((acc, group) => {
      if (!acc[group.provider]) {
        acc[group.provider] = [];
      }
      acc[group.provider].push(group);
      return acc;
    }, {} as Record<string, UserConfigGroupDto[]>);
  });

  // 获取所有提供商列表
  const allProviders = computed(() => {
    return Array.from(new Set(configGroups.value.map(group => group.provider)));
  });

  // 获取当前分组的活跃密钥
  const currentGroupActiveKeys = computed(() => {
    return apiKeys.value.filter(key => key.isActive);
  });

  // 检查是否有数据需要刷新（5分钟缓存）
  const needsRefresh = computed(() => {
    const now = Date.now();
    const cacheTime = 5 * 60 * 1000; // 5分钟
    return {
      groups: now - lastFetchTime.groups > cacheTime,
      keys: now - lastFetchTime.keys > cacheTime,
      compatibleKeys: now - lastFetchTime.compatibleKeys > cacheTime,
      stats: now - lastFetchTime.stats > cacheTime
    };
  });

  // Actions (函数方法)
  // 配置分组管理
  const fetchConfigGroups = async (force = false) => {
    if (!force && !needsRefresh.value.groups && configGroups.value.length > 0) {
      return configGroups.value;
    }

    loading.groups = true;
    error.value = null;

    try {
      configGroups.value = await AiConfigApi.getUserConfigGroups();
      lastFetchTime.groups = Date.now();
      return configGroups.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "获取配置分组失败";
      throw err;
    } finally {
      loading.groups = false;
    }
  };

  const createConfigGroup = async (config: UserConfigGroupDto) => {
    loading.groups = true;
    error.value = null;

    try {
      const newGroup = await AiConfigApi.createOrUpdateUserConfigGroup(config);
      configGroups.value.push(newGroup);
      lastFetchTime.groups = Date.now();
      return newGroup;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "创建配置分组失败";
      throw err;
    } finally {
      loading.groups = false;
    }
  };

  const updateConfigGroup = async (config: UserConfigGroupDto) => {
    loading.groups = true;
    error.value = null;

    try {
      const updatedGroup = await AiConfigApi.createOrUpdateUserConfigGroup(config);
      const index = configGroups.value.findIndex(g => g.id === updatedGroup.id);
      if (index !== -1) {
        configGroups.value[index] = updatedGroup;
      }
      if (currentGroup.value?.id === updatedGroup.id) {
        currentGroup.value = updatedGroup;
      }
      lastFetchTime.groups = Date.now();
      return updatedGroup;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "更新配置分组失败";
      throw err;
    } finally {
      loading.groups = false;
    }
  };

  const deleteConfigGroup = async (groupId: number) => {
    loading.groups = true;
    error.value = null;

    try {
      await AiConfigApi.deleteUserConfigGroup(groupId);
      configGroups.value = configGroups.value.filter(g => g.id !== groupId);
      if (currentGroup.value?.id === groupId) {
        currentGroup.value = null;
        apiKeys.value = [];
      }
      lastFetchTime.groups = Date.now();
    } catch (err) {
      error.value = err instanceof Error ? err.message : "删除配置分组失败";
      throw err;
    } finally {
      loading.groups = false;
    }
  };

  // 设置当前分组
  const setCurrentGroup = async (group: UserConfigGroupDto | null) => {
    currentGroup.value = group;
    if (group) {
      await fetchApiKeys(group.id!);
    } else {
      apiKeys.value = [];
    }
  };

  // API密钥管理
  const fetchApiKeys = async (configGroupId: number, force = false) => {
    if (!force && !needsRefresh.value.keys && apiKeys.value.length > 0) {
      return apiKeys.value;
    }

    loading.keys = true;
    error.value = null;

    try {
      apiKeys.value = await AiConfigApi.getUserApiKeysByConfigGroupId(configGroupId);
      lastFetchTime.keys = Date.now();
      return apiKeys.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "获取API密钥失败";
      throw err;
    } finally {
      loading.keys = false;
    }
  };

  const batchAddApiKeys = async (request: BatchAddApiKeyRequest) => {
    loading.keys = true;
    error.value = null;

    try {
      const result = await AiConfigApi.batchAddApiKeys(request);
      // 刷新当前分组的API密钥列表
      if (currentGroup.value?.id === request.configGroupId) {
        await fetchApiKeys(request.configGroupId, true);
      }
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "批量添加API密钥失败";
      throw err;
    } finally {
      loading.keys = false;
    }
  };

  const testApiKey = async (keyId: number): Promise<ApiKeyTestResult> => {
    loading.testing = true;
    error.value = null;

    try {
      const result = await AiConfigApi.testApiKey(keyId);
      // 更新本地密钥状态
      const keyIndex = apiKeys.value.findIndex(key => key.id === keyId);
      if (keyIndex !== -1) {
        apiKeys.value[keyIndex].isActive = result.valid;
      }
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "测试API密钥失败";
      throw err;
    } finally {
      loading.testing = false;
    }
  };

  const updateApiKey = async (keyId: number, updateData: ApiKeyUpdateRequest) => {
    loading.keys = true;
    error.value = null;

    try {
      const result = await AiConfigApi.updateApiKey(keyId, updateData);
      // 更新本地状态中的密钥信息
      const index = apiKeys.value.findIndex(key => key.id === keyId);
      if (index !== -1) {
        apiKeys.value[index] = result;
      }
      lastFetchTime.keys = Date.now();
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "更新API密钥失败";
      throw err;
    } finally {
      loading.keys = false;
    }
  };

  const deleteApiKey = async (keyId: number) => {
    loading.keys = true;
    error.value = null;

    try {
      await AiConfigApi.deleteApiKey(keyId);
      apiKeys.value = apiKeys.value.filter(key => key.id !== keyId);
      lastFetchTime.keys = Date.now();
    } catch (err) {
      error.value = err instanceof Error ? err.message : "删除API密钥失败";
      throw err;
    } finally {
      loading.keys = false;
    }
  };

  // 兼容密钥管理
  const fetchCompatibleKeys = async (force = false) => {
    if (!force && !needsRefresh.value.compatibleKeys && compatibleKeys.value.length > 0) {
      return compatibleKeys.value;
    }

    loading.compatibleKeys = true;
    error.value = null;

    try {
      compatibleKeys.value = await AiConfigApi.getCompatibleKeys();
      lastFetchTime.compatibleKeys = Date.now();
      return compatibleKeys.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "获取兼容密钥失败";
      throw err;
    } finally {
      loading.compatibleKeys = false;
    }
  };

  const generateCompatibleKey = async (request: CreateCompatibleKeyRequest) => {
    loading.compatibleKeys = true;
    error.value = null;

    try {
      const result = await AiConfigApi.generateCompatibleKey(request);
      compatibleKeys.value.push(result);
      lastFetchTime.compatibleKeys = Date.now();
      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : "生成兼容密钥失败";
      throw err;
    } finally {
      loading.compatibleKeys = false;
    }
  };

  // 清除错误
  const clearError = () => {
    error.value = null;
  };

  // 重置状态
  const resetState = () => {
    configGroups.value = [];
    currentGroup.value = null;
    apiKeys.value = [];
    currentApiKey.value = null;
    compatibleKeys.value = [];
    keyStats.value = null;
    loading.groups = false;
    loading.keys = false;
    loading.compatibleKeys = false;
    loading.stats = false;
    loading.testing = false;
    error.value = null;
    lastFetchTime.groups = 0;
    lastFetchTime.keys = 0;
    lastFetchTime.compatibleKeys = 0;
    lastFetchTime.stats = 0;
  };

  // 返回所有需要暴露的状态和方法
  return {
    // 状态
    configGroups,
    currentGroup,
    apiKeys,
    currentApiKey,
    compatibleKeys,
    keyStats,
    loading,
    error,
    lastFetchTime,

    // 计算属性
    currentGroupApiKeyCount,
    activeApiKeyCount,
    inactiveApiKeyCount,
    groupsByProvider,
    allProviders,
    currentGroupActiveKeys,
    needsRefresh,

    // 方法
    fetchConfigGroups,
    createConfigGroup,
    updateConfigGroup,
    deleteConfigGroup,
    setCurrentGroup,
    fetchApiKeys,
    batchAddApiKeys,
    testApiKey,
    updateApiKey,
    deleteApiKey,
    fetchCompatibleKeys,
    generateCompatibleKey,
    clearError,
    resetState
  };
});
