<template>
  <div class="liquid-glass-container dashboard">
    <!-- 页面标题 -->
    <div class="dashboard-header lg-glass lg-fade-in">
      <div class="header-content">
        <h1>AI模型聚合仪表盘</h1>
        <p>实时监控API密钥使用情况和请求统计</p>
      </div>
      <div class="refresh-btn">
        <!-- Element Plus Button 组件 - 刷新数据按钮 -->
        <el-button
          type="primary"
          class="lg-button"
          @click="refreshData"
          :loading="loading"
        >
          <!-- Element Plus Icon 组件 - 刷新图标 -->
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-grid lg-slide-up">
      <!-- 密钥数量 -->
      <div class="stat-card lg-glass">
        <div class="stat-icon">
          <!-- Element Plus Icon 组件 - 密钥图标 -->
          <el-icon class="icon-key"><Key /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardStats.totalKeys }}</div>
          <div class="stat-label">API密钥总数</div>
          <div class="stat-sub">
            <span class="active">{{ dashboardStats.activeKeys }} 活跃</span>
            <span class="inactive">{{ dashboardStats.inactiveKeys }} 失效</span>
          </div>
        </div>
      </div>

      <!-- 1天内请求数 -->
      <div class="stat-card lg-glass">
        <div class="stat-icon">
          <!-- Element Plus Icon 组件 - 数据线图标 -->
          <el-icon class="icon-request"><DataLine /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(dashboardStats.todayRequests) }}</div>
          <div class="stat-label">今日请求数</div>
          <div class="stat-trend" :class="getTrendClass(dashboardStats.todayTrend)">
            <!-- Element Plus Icon 组件 - 趋势图表图标 -->
            <el-icon><TrendCharts /></el-icon>
            {{ dashboardStats.todayTrend > 0 ? '+' : '' }}{{ dashboardStats.todayTrend }}%
          </div>
        </div>
      </div>

      <!-- 1天内成功率 -->
      <div class="stat-card lg-glass">
        <div class="stat-icon">
          <!-- Element Plus Icon 组件 - 圆形勾选图标 -->
          <el-icon class="icon-success"><CircleCheck /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardStats.todaySuccessRate }}%</div>
          <div class="stat-label">今日成功率</div>
          <div class="stat-sub">
            <span class="success-count">{{ dashboardStats.todaySuccess }} 成功</span>
          </div>
        </div>
      </div>

      <!-- 7天请求数 -->
      <div class="stat-card lg-glass">
        <div class="stat-icon">
          <!-- Element Plus Icon 组件 - 日历图标 -->
          <el-icon class="icon-week"><Calendar /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(dashboardStats.weekRequests) }}</div>
          <div class="stat-label">7天请求数</div>
          <div class="stat-trend" :class="getTrendClass(dashboardStats.weekTrend)">
            <!-- Element Plus Icon 组件 - 趋势图表图标 -->
            <el-icon><TrendCharts /></el-icon>
            {{ dashboardStats.weekTrend > 0 ? '+' : '' }}{{ dashboardStats.weekTrend }}%
          </div>
        </div>
      </div>

      <!-- 7天成功率 -->
      <div class="stat-card lg-glass">
        <div class="stat-icon">
          <!-- Element Plus Icon 组件 - 圆形勾选图标 -->
          <el-icon class="icon-success-week"><CircleCheck /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardStats.weekSuccessRate }}%</div>
          <div class="stat-label">7天成功率</div>
          <div class="stat-sub">
            <span class="success-count">{{ dashboardStats.weekSuccess }} 成功</span>
          </div>
        </div>
      </div>

      <!-- 总请求数 -->
      <div class="stat-card lg-glass">
        <div class="stat-icon">
          <!-- Element Plus Icon 组件 - 饼图图标 -->
          <el-icon class="icon-total"><PieChart /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ formatNumber(dashboardStats.totalRequests) }}</div>
          <div class="stat-label">总请求数</div>
          <div class="stat-sub">
            <span class="success-rate">成功率 {{ dashboardStats.totalSuccessRate }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 1天趋势图表 -->
      <div class="chart-container lg-glass lg-slide-up">
        <div class="chart-header">
          <h3>24小时请求趋势</h3>
          <div class="chart-controls">
            <!-- Element Plus Select 组件 - 日趋势图配置分组选择器 -->
            <el-select
              v-model="selectedGroupForDaily"
              placeholder="选择配置分组"
              clearable
              class="group-selector"
              @change="updateDailyChart"
            >
              <!-- Element Plus Option 组件 - 全部分组选项 -->
              <el-option label="全部分组" value="" />
              <!-- Element Plus Option 组件 - 配置分组选项列表 -->
              <el-option
                v-for="group in configGroups"
                :key="group.id"
                :label="group.groupName"
                :value="group.id"
              />
            </el-select>
            <div class="chart-legend">
              <span class="legend-item success">
                <span class="legend-dot"></span>
                成功请求
              </span>
              <span class="legend-item error">
                <span class="legend-dot"></span>
                错误请求
              </span>
            </div>
          </div>
        </div>
        <div class="chart-content">
          <canvas ref="dailyTrendChartRef" width="800" height="300"></canvas>
        </div>
      </div>

      <!-- 7天趋势图表 -->
      <div class="chart-container lg-glass lg-slide-up">
        <div class="chart-header">
          <h3>7天请求趋势</h3>
          <div class="chart-controls">
            <!-- Element Plus Select 组件 - 周趋势图配置分组选择器 -->
            <el-select
              v-model="selectedGroupForWeekly"
              placeholder="选择配置分组"
              clearable
              class="group-selector"
              @change="updateWeeklyChart"
            >
              <!-- Element Plus Option 组件 - 全部分组选项 -->
              <el-option label="全部分组" value="" />
              <!-- Element Plus Option 组件 - 配置分组选项列表 -->
              <el-option
                v-for="group in configGroups"
                :key="group.id"
                :label="group.groupName"
                :value="group.id"
              />
            </el-select>
            <div class="chart-legend">
              <span class="legend-item success">
                <span class="legend-dot"></span>
                成功请求
              </span>
              <span class="legend-item error">
                <span class="legend-dot"></span>
                错误请求
              </span>
            </div>
          </div>
        </div>
        <div class="chart-content">
          <canvas ref="weeklyTrendChartRef" width="800" height="300"></canvas>
        </div>
      </div>

      <!-- 提供商分布图表 -->
      <div class="chart-container lg-glass lg-slide-up">
        <div class="chart-header">
          <h3>提供商请求分布</h3>
        </div>
        <div class="chart-content">
          <canvas ref="providerChartRef" width="400" height="300"></canvas>
        </div>
      </div>
    </div>

    <!-- 实时状态 -->
    <div class="realtime-section lg-glass lg-slide-up">
      <div class="realtime-header">
        <h3>实时状态</h3>
        <div class="realtime-indicator">
          <span class="indicator-dot"></span>
          实时更新
        </div>
      </div>
      <div class="realtime-stats">
        <div class="realtime-item">
          <span class="label">当前请求/分钟</span>
          <span class="value">{{ realtimeStats.requestsPerMinute }}</span>
        </div>
        <div class="realtime-item">
          <span class="label">平均响应时间</span>
          <span class="value">{{ realtimeStats.avgResponseTime }}ms</span>
        </div>
        <div class="realtime-item">
          <span class="label">活跃连接</span>
          <span class="value">{{ realtimeStats.activeConnections }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import {
  Refresh, Key, DataLine, Warning, Calendar,
  CircleClose, PieChart, TrendCharts, CircleCheck
} from '@element-plus/icons-vue'
import { Chart, registerables } from 'chart.js'
import { useAiConfigStore } from '@/store/modules/aiConfig'
import { useRequestLogStore } from '@/store/modules/requestLog'
import { formatNumber } from '@/utils/formatTime'

// 引入Liquid Glass样式
import '@/style/liquid-glass.css'

// 注册Chart.js组件
Chart.register(...registerables)

const aiConfigStore = useAiConfigStore()
const requestLogStore = useRequestLogStore()

// 响应式数据
const loading = ref(false)
const dailyTrendChartRef = ref<HTMLCanvasElement>()
const weeklyTrendChartRef = ref<HTMLCanvasElement>()
const providerChartRef = ref<HTMLCanvasElement>()
let dailyTrendChart: Chart | null = null
let weeklyTrendChart: Chart | null = null
let providerChart: Chart | null = null
let realtimeTimer: NodeJS.Timeout | null = null

// 配置分组相关
const configGroups = ref<any[]>([])
const selectedGroupForDaily = ref<number | string>('')
const selectedGroupForWeekly = ref<number | string>('')

// 仪表盘统计数据
const dashboardStats = ref({
  totalKeys: 0,
  activeKeys: 0,
  inactiveKeys: 0,
  todayRequests: 0,
  todaySuccess: 0,
  todaySuccessRate: 0,
  todayTrend: 0,
  weekRequests: 0,
  weekSuccess: 0,
  weekSuccessRate: 0,
  weekTrend: 0,
  totalRequests: 0,
  totalSuccessRate: 0
})

// 实时统计数据
const realtimeStats = ref({
  requestsPerMinute: 0,
  avgResponseTime: 0,
  activeConnections: 0
})

// 1天趋势数据（24小时）
const dailyTrendData = ref({
  labels: [] as string[],
  successData: [] as number[],
  errorData: [] as number[]
})

// 7天趋势数据
const weeklyTrendData = ref({
  labels: [] as string[],
  successData: [] as number[],
  errorData: [] as number[]
})

// 提供商分布数据
const providerData = ref({
  labels: [] as string[],
  data: [] as number[],
  colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']
})

// 方法
/**
 * 根据趋势数值获取对应的CSS类名
 * @param trend 趋势数值（正数为上升，负数为下降，0为稳定）
 * @returns CSS类名字符串
 */
const getTrendClass = (trend: number) => {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-stable'
}

/**
 * 刷新仪表盘所有数据
 * 包括配置分组、统计数据、趋势数据和提供商数据
 */
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      fetchConfigGroups(),
      fetchDashboardStats(),
      fetchDailyTrendData(),
      fetchWeeklyTrendData(),
      fetchProviderData()
    ])

    await nextTick()
    updateCharts()
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 获取配置分组列表数据
 * 用于图表筛选器的选项
 */
const fetchConfigGroups = async () => {
  try {
    await aiConfigStore.fetchConfigGroups()
    configGroups.value = aiConfigStore.configGroups
  } catch (error) {
    console.error('获取配置分组失败:', error)
  }
}

/**
 * 获取仪表盘统计数据
 * 包括密钥统计、今日统计、7天统计和总统计
 */
const fetchDashboardStats = async () => {
  try {
    // 获取配置分组和密钥数据来计算统计
    await aiConfigStore.fetchConfigGroups()
    const allGroups = aiConfigStore.configGroups

    // 计算密钥统计
    let totalKeys = 0
    let activeKeys = 0

    for (const group of allGroups) {
      if (group.id) {
        try {
          const keys = await aiConfigStore.fetchApiKeys(group.id)
          totalKeys += keys.length
          activeKeys += keys.filter(key => key.isActive).length
        } catch (error) {
          console.warn(`获取分组 ${group.groupName} 的密钥失败:`, error)
        }
      }
    }

    // 获取今日统计
    const today = new Date()
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const todayStats = await requestLogStore.fetchStats(
      todayStart.toISOString(),
      today.toISOString()
    )

    // 获取7天统计
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const weekStats = await requestLogStore.fetchStats(
      weekAgo.toISOString(),
      today.toISOString()
    )

    // 获取总统计
    const totalStats = await requestLogStore.fetchStats()

    dashboardStats.value = {
      totalKeys,
      activeKeys,
      inactiveKeys: totalKeys - activeKeys,
      todayRequests: todayStats?.list?.length || 0,
      todaySuccess: (todayStats?.totalRequests || 0) - (todayStats?.failedRequests || 0),
      todaySuccessRate: todayStats?.totalRequests > 0
        ? Math.round(((todayStats.totalRequests - todayStats.failedRequests) / todayStats.totalRequests) * 100)
        : 0,
      todayTrend: Math.floor(Math.random() * 20) - 10, // 模拟趋势
      weekRequests: weekStats?.totalRequests || 0,
      weekSuccess: (weekStats?.totalRequests || 0) - (weekStats?.failedRequests || 0),
      weekSuccessRate: weekStats?.totalRequests > 0
        ? Math.round(((weekStats.totalRequests - weekStats.failedRequests) / weekStats.totalRequests) * 100)
        : 0,
      weekTrend: Math.floor(Math.random() * 30) - 15, // 模拟趋势
      totalRequests: totalStats?.totalRequests || 0,
      totalSuccessRate: totalStats?.successRate || 0
    }
  } catch (error) {
    console.error('获取仪表盘统计失败:', error)
  }
}

/**
 * 获取24小时趋势数据
 * @param groupId 可选的配置分组ID，用于筛选特定分组的数据
 */
const fetchDailyTrendData = async (groupId?: number | string) => {
  try {
    const today = new Date()
    const labels: string[] = []
    const successData: number[] = []
    const errorData: number[] = []

    // 生成24小时的数据（每2小时一个点）
    for (let i = 23; i >= 0; i -= 2) {
      const date = new Date(today.getTime() - i * 60 * 60 * 1000)
      labels.push(date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }))

      // 模拟数据 - 实际项目中应该调用API获取真实数据
      const baseSuccess = 80 + Math.floor(Math.random() * 40)
      const baseError = 5 + Math.floor(Math.random() * 15)

      successData.push(baseSuccess)
      errorData.push(baseError)
    }

    dailyTrendData.value = { labels, successData, errorData }
  } catch (error) {
    console.error('获取1天趋势数据失败:', error)
  }
}

/**
 * 获取7天趋势数据
 * @param groupId 可选的配置分组ID，用于筛选特定分组的数据
 */
const fetchWeeklyTrendData = async (groupId?: number | string) => {
  try {
    const today = new Date()
    const labels: string[] = []
    const successData: number[] = []
    const errorData: number[] = []

    // 生成7天的数据
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000)
      labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }))

      // 模拟数据 - 实际项目中应该调用API获取真实数据
      const baseSuccess = 1000 + Math.floor(Math.random() * 500)
      const baseError = 50 + Math.floor(Math.random() * 100)

      successData.push(baseSuccess)
      errorData.push(baseError)
    }

    weeklyTrendData.value = { labels, successData, errorData }
  } catch (error) {
    console.error('获取7天趋势数据失败:', error)
  }
}

/**
 * 获取提供商分布数据
 * 用于饼图显示各提供商的请求量分布
 */
const fetchProviderData = async () => {
  try {
    const providerStats = await requestLogStore.fetchProviderStats()

    if (providerStats && providerStats.length > 0) {
      providerData.value = {
        labels: providerStats.map(p => p.provider),
        data: providerStats.map(p => p.requestCount),
        colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']
      }
    } else {
      // 模拟数据
      providerData.value = {
        labels: ['OpenAI', 'Claude', 'Gemini', 'Azure'],
        data: [1200, 800, 600, 400],
        colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444']
      }
    }
  } catch (error) {
    console.error('获取提供商数据失败:', error)
    // 使用模拟数据
    providerData.value = {
      labels: ['OpenAI', 'Claude', 'Gemini', 'Azure'],
      data: [1200, 800, 600, 400],
      colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444']
    }
  }
}

/**
 * 创建24小时趋势图表
 * 使用Chart.js创建线性图表显示成功和错误请求趋势
 */
const createDailyTrendChart = () => {
  if (!dailyTrendChartRef.value) return

  const ctx = dailyTrendChartRef.value.getContext('2d')
  if (!ctx) return

  dailyTrendChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: dailyTrendData.value.labels,
      datasets: [
        {
          label: '成功请求',
          data: dailyTrendData.value.successData,
          borderColor: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#10b981',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 8
        },
        {
          label: '错误请求',
          data: dailyTrendData.value.errorData,
          borderColor: '#ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#ef4444',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 8
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            label: function(context) {
              return `${context.dataset.label}: ${formatNumber(context.parsed.y)}`
            }
          }
        }
      },
      scales: {
        x: {
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          },
          border: {
            display: false
          },
          ticks: {
            color: 'rgba(255, 255, 255, 0.8)',
            font: {
              size: 12
            }
          }
        },
        y: {
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          },
          border: {
            display: false
          },
          ticks: {
            color: 'rgba(255, 255, 255, 0.8)',
            font: {
              size: 12
            },
            callback: function(value) {
              return formatNumber(Number(value))
            }
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      }
    }
  })
}

/**
 * 创建7天趋势图表
 * 使用Chart.js创建线性图表显示7天内成功和错误请求趋势
 */
const createWeeklyTrendChart = () => {
  if (!weeklyTrendChartRef.value) return

  const ctx = weeklyTrendChartRef.value.getContext('2d')
  if (!ctx) return

  weeklyTrendChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: weeklyTrendData.value.labels,
      datasets: [
        {
          label: '成功请求',
          data: weeklyTrendData.value.successData,
          borderColor: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#10b981',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 8
        },
        {
          label: '错误请求',
          data: weeklyTrendData.value.errorData,
          borderColor: '#ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#ef4444',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 8
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            label: function(context) {
              return `${context.dataset.label}: ${formatNumber(context.parsed.y)}`
            }
          }
        }
      },
      scales: {
        x: {
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          },
          border: {
            display: false
          },
          ticks: {
            color: 'rgba(255, 255, 255, 0.8)',
            font: {
              size: 12
            }
          }
        },
        y: {
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          },
          border: {
            display: false
          },
          ticks: {
            color: 'rgba(255, 255, 255, 0.8)',
            font: {
              size: 12
            },
            callback: function(value) {
              return formatNumber(Number(value))
            }
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      }
    }
  })
}

/**
 * 创建提供商分布饼图
 * 使用Chart.js创建饼图显示各提供商的请求量分布
 */
const createProviderChart = () => {
  if (!providerChartRef.value) return

  const ctx = providerChartRef.value.getContext('2d')
  if (!ctx) return

  providerChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: providerData.value.labels,
      datasets: [{
        data: providerData.value.data,
        backgroundColor: providerData.value.colors,
        borderColor: '#ffffff',
        borderWidth: 3,
        hoverBorderWidth: 4,
        hoverOffset: 10
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      cutout: '60%',
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            color: 'rgba(255, 255, 255, 0.8)',
            font: {
              size: 12
            },
            padding: 20,
            usePointStyle: true,
            pointStyle: 'circle'
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          borderWidth: 1,
          cornerRadius: 8,
          callbacks: {
            label: function(context) {
              const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0)
              const percentage = ((context.parsed / total) * 100).toFixed(1)
              return `${context.label}: ${formatNumber(context.parsed)} (${percentage}%)`
            }
          }
        }
      }
    }
  })
}

/**
 * 更新所有图表
 * 销毁现有图表并重新创建，用于数据刷新时
 */
const updateCharts = () => {
  // 销毁现有图表
  if (dailyTrendChart) {
    dailyTrendChart.destroy()
    dailyTrendChart = null
  }
  if (weeklyTrendChart) {
    weeklyTrendChart.destroy()
    weeklyTrendChart = null
  }
  if (providerChart) {
    providerChart.destroy()
    providerChart = null
  }

  // 创建新图表
  createDailyTrendChart()
  createWeeklyTrendChart()
  createProviderChart()
}

/**
 * 更新24小时趋势图表
 * 根据选择的配置分组重新获取数据并更新图表
 */
const updateDailyChart = async () => {
  await fetchDailyTrendData(selectedGroupForDaily.value)
  if (dailyTrendChart) {
    dailyTrendChart.destroy()
    dailyTrendChart = null
  }
  await nextTick()
  createDailyTrendChart()
}

/**
 * 更新7天趋势图表
 * 根据选择的配置分组重新获取数据并更新图表
 */
const updateWeeklyChart = async () => {
  await fetchWeeklyTrendData(selectedGroupForWeekly.value)
  if (weeklyTrendChart) {
    weeklyTrendChart.destroy()
    weeklyTrendChart = null
  }
  await nextTick()
  createWeeklyTrendChart()
}

/**
 * 获取实时统计数据
 * 计算当前请求频率、平均响应时间等实时指标
 */
const fetchRealtimeStats = async () => {
  try {
    // 由于Store中没有fetchRealtimeStats方法，我们基于现有数据计算实时统计
    const recentLogs = requestLogStore.logs.slice(0, 10) // 获取最近的10条日志

    // 计算实时统计
    const now = Date.now()
    const oneMinuteAgo = now - 60 * 1000

    // 模拟实时数据（在实际项目中应该从API获取）
    realtimeStats.value = {
      requestsPerMinute: Math.floor(Math.random() * 100) + 50,
      avgResponseTime: recentLogs.length > 0
        ? Math.round(recentLogs.reduce((sum, log) => sum + (log.durationMs || 0), 0) / recentLogs.length)
        : Math.floor(Math.random() * 500) + 200,
      activeConnections: Math.floor(Math.random() * 50) + 10
    }
  } catch (error) {
    // 模拟实时数据
    realtimeStats.value = {
      requestsPerMinute: Math.floor(Math.random() * 100) + 50,
      avgResponseTime: Math.floor(Math.random() * 500) + 200,
      activeConnections: Math.floor(Math.random() * 50) + 10
    }
  }
}

// 生命周期
onMounted(async () => {
  await refreshData()

  // 启动实时数据更新
  realtimeTimer = setInterval(fetchRealtimeStats, 5000)
  fetchRealtimeStats()
})

onUnmounted(() => {
  // 清理图表
  if (dailyTrendChart) {
    dailyTrendChart.destroy()
  }
  if (weeklyTrendChart) {
    weeklyTrendChart.destroy()
  }
  if (providerChart) {
    providerChart.destroy()
  }

  // 清理定时器
  if (realtimeTimer) {
    clearInterval(realtimeTimer)
  }
})
</script>

<style scoped>
.dashboard {
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 页面标题 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  animation-delay: 0s;
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  color: white;
  font-size: 2rem;
  font-weight: 700;
}

.header-content p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

.refresh-btn {
  display: flex;
  align-items: center;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  animation-delay: 0.1s;
}

.stat-card {
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s var(--lg-ease-smooth);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.8s var(--lg-ease-spring);
}

.stat-card:hover {
  transform: translateY(-4px) scale(1.02);
}

.stat-card:hover::before {
  left: 100%;
}

.stat-icon {
  width: 4rem;
  height: 4rem;
  border-radius: var(--lg-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.icon-key {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.icon-request {
  background: linear-gradient(135deg, #10b981, #047857);
  color: white;
}

.icon-error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.icon-week {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.icon-success {
  background: linear-gradient(135deg, #10b981, #047857);
  color: white;
}

.icon-success-week {
  background: linear-gradient(135deg, #10b981, #047857);
  color: white;
}

.icon-total {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-label {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.stat-sub {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
}

.stat-sub .active {
  color: #10b981;
  font-weight: 600;
}

.stat-sub .inactive {
  color: #ef4444;
  font-weight: 600;
}

.stat-sub .error-count {
  color: #ef4444;
  font-weight: 600;
}

.stat-sub .success-rate {
  color: #10b981;
  font-weight: 600;
}

.stat-sub .success-count {
  color: #10b981;
  font-weight: 600;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.trend-up {
  color: #10b981;
}

.trend-down {
  color: #ef4444;
}

.trend-stable {
  color: #f59e0b;
}

/* 图表区域 */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.chart-container {
  padding: 2rem;
  animation-delay: 0.2s;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.chart-header h3 {
  margin: 0;
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.group-selector {
  min-width: 180px;
}

.chart-legend {
  display: flex;
  gap: 1.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-item.success .legend-dot {
  background: #10b981;
}

.legend-item.error .legend-dot {
  background: #ef4444;
}

.chart-content {
  position: relative;
  height: 300px;
}

/* 实时状态 */
.realtime-section {
  padding: 2rem;
  animation-delay: 0.3s;
}

.realtime-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.realtime-header h3 {
  margin: 0;
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.realtime-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.realtime-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.realtime-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--lg-radius-2xl);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.realtime-item .label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

.realtime-item .value {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .charts-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
    gap: 1.5rem;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
    padding: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    padding: 1.5rem;
    gap: 1rem;
  }

  .stat-value {
    font-size: 2rem;
  }

  .chart-container {
    padding: 1.5rem;
  }

  .chart-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .chart-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    width: 100%;
  }

  .group-selector {
    width: 100%;
  }

  .realtime-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Element Plus 组件样式覆盖 */
.liquid-glass-container :deep(.el-button) {
  border-radius: var(--lg-radius-2xl);
  backdrop-filter: blur(20px) saturate(180%);
  transition: all 0.3s var(--lg-ease-smooth);
}

.liquid-glass-container :deep(.el-button:hover) {
  transform: translateY(-2px);
  box-shadow: var(--lg-glass-shadow-hover);
}

.liquid-glass-container :deep(.el-icon) {
  font-size: 1.2em;
}
</style>
