<template>
  <div class="liquid-glass-container ai-key-management">
    <div class="layout-container">
      <!-- 左侧分组列表 -->
      <div class="sidebar lg-glass">
        <div class="sidebar-header">
          <h3>配置分组</h3>
          <!-- Element Plus Button 组件 - 新建分组按钮 -->
          <el-button
            type="primary"
            class="lg-button"
            @click="showCreateGroupDialog = true"
          >
            新建分组
          </el-button>
        </div>

        <div class="group-search">
          <!-- Element Plus Input 组件 - 分组搜索输入框 -->
          <el-input
            v-model="groupSearchText"
            placeholder="搜索分组..."
            prefix-icon="Search"
            clearable
            class="lg-input"
          />
        </div>

        <div class="group-list">
          <div
            v-for="group in filteredGroups"
            :key="group.id"
            class="group-item lg-glass"
            :class="{ active: currentGroup?.id === group.id }"
            @click="selectGroup(group)"
          >
            <div class="group-info">
              <div class="group-name">{{ group.groupName }}</div>
              <div class="group-provider">
                <el-tag class="lg-tag" size="small">{{ group.provider }}</el-tag>
              </div>
            </div>
            <div class="group-actions">
              <el-button
                size="small"
                text
                class="lg-button"
                @click.stop="editGroup(group)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                text
                type="danger"
                class="lg-button"
                @click.stop="deleteGroup(group)"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="main-content">
        <div v-if="!currentGroup" class="empty-state lg-glass lg-fade-in">
          <el-empty description="请选择一个配置分组" />
        </div>

        <div v-else class="group-detail lg-fade-in">
          <!-- 分组信息 -->
          <div class="group-header lg-glass">
            <div class="group-title">
              <h2>{{ currentGroup.groupName }}</h2>
              <el-tag class="lg-tag">{{ currentGroup.provider }}</el-tag>
            </div>
            <div class="group-stats">
              <div class="stat-item lg-glass">
                <div class="stat-value">{{ apiKeys.length }}</div>
                <div class="stat-label">API密钥数量</div>
              </div>
              <div class="stat-item lg-glass">
                <div class="stat-value">{{ activeKeyCount }}</div>
                <div class="stat-label">活跃密钥</div>
              </div>
              <div class="stat-item lg-glass">
                <div class="stat-value">{{ inactiveKeyCount }}</div>
                <div class="stat-label">失效密钥</div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-bar">
            <el-button
              type="primary"
              class="lg-button"
              @click="showBatchUploadDialog = true"
            >
              批量添加密钥
            </el-button>
            <el-button
              class="lg-button"
              @click="showCompatibleKeyDialog = true"
            >
              生成兼容密钥
            </el-button>
            <el-button
              class="lg-button"
              @click="refreshApiKeys"
              :loading="loading.keys"
            >
              刷新
            </el-button>
          </div>

          <!-- API密钥列表 -->
          <div class="api-keys-section lg-glass">
            <div class="section-header">
              <h3>API密钥列表</h3>
              <div class="header-actions">
                <el-input
                  v-model="keySearchText"
                  placeholder="搜索密钥..."
                  prefix-icon="Search"
                  clearable
                  class="lg-input search-input"
                />
              </div>
            </div>

            <el-table
              :data="filteredApiKeys"
              stripe
              class="lg-table"
              v-loading="loading.keys"
            >
              <el-table-column prop="keyName" label="密钥名称" min-width="150" />
              <el-table-column prop="maskedApiKey" label="密钥" min-width="200">
                <template #default="{ row }">
                  <code class="masked-key">{{ row.maskedApiKey }}</code>
                </template>
              </el-table-column>
              <el-table-column prop="isActive" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag
                    :type="row.isActive ? 'success' : 'danger'"
                    class="lg-tag"
                  >
                    {{ row.isActive ? '有效' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="usageCount" label="使用次数" width="120" />
              <el-table-column prop="errorCount" label="错误次数" width="120" />
              <el-table-column prop="lastUsedAt" label="最后使用时间" width="180">
                <template #default="{ row }">
                  {{ formatDateTime(row.lastUsedAt) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button
                    size="small"
                    text
                    class="lg-button"
                    @click="testApiKey(row)"
                    :loading="testingKeys.includes(row.id)"
                  >
                    测试
                  </el-button>
                  <el-button
                    size="small"
                    text
                    :type="row.isActive ? 'warning' : 'success'"
                    class="lg-button"
                    @click="toggleApiKeyStatus(row)"
                  >
                    {{ row.isActive ? '禁用' : '启用' }}
                  </el-button>
                  <el-button
                    size="small"
                    text
                    type="danger"
                    class="lg-button"
                    @click="deleteApiKey(row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑分组对话框 -->
    <ConfigGroupDialog
      v-model="showCreateGroupDialog"
      :group="editingGroup"
      @success="handleGroupSaved"
    />

    <!-- 批量上传密钥对话框 -->
    <BatchUploadDialog
      v-model="showBatchUploadDialog"
      :config-group-id="currentGroup?.id"
      @success="handleBatchUploadSuccess"
    />

    <!-- 兼容密钥生成对话框 -->
    <CompatibleKeyDialog
      v-model="showCompatibleKeyDialog"
      :config-groups="configGroups"
      @success="handleCompatibleKeyGenerated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAiConfigStore } from '@/store/modules/aiConfig'
import { formatDateTime } from '@/utils/formatTime'
import ConfigGroupDialog from './components/ConfigGroupDialog.vue'
import BatchUploadDialog from './components/BatchUploadDialog.vue'
import CompatibleKeyDialog from './components/CompatibleKeyDialog.vue'
import type { UserConfigGroupDto, ApiKeyDto } from '@/types/aiConfig'

// 引入Liquid Glass样式
import '@/style/liquid-glass.css'

const aiConfigStore = useAiConfigStore()

// 响应式数据
const groupSearchText = ref('')
const keySearchText = ref('')
const showCreateGroupDialog = ref(false)
const showBatchUploadDialog = ref(false)
const showCompatibleKeyDialog = ref(false)
const editingGroup = ref<UserConfigGroupDto | null>(null)
const testingKeys = ref<number[]>([]) // 正在测试的密钥ID列表

// 计算属性
const configGroups = computed(() => aiConfigStore.configGroups)
const currentGroup = computed(() => aiConfigStore.currentGroup)
const apiKeys = computed(() => aiConfigStore.apiKeys)
const loading = computed(() => aiConfigStore.loading)

const filteredGroups = computed(() => {
  if (!groupSearchText.value) return configGroups.value
  return configGroups.value.filter(group =>
    group.groupName.toLowerCase().includes(groupSearchText.value.toLowerCase()) ||
    group.provider.toLowerCase().includes(groupSearchText.value.toLowerCase())
  )
})

const filteredApiKeys = computed(() => {
  if (!keySearchText.value) return apiKeys.value
  return apiKeys.value.filter(key =>
    key.keyName.toLowerCase().includes(keySearchText.value.toLowerCase()) ||
    key.maskedApiKey.toLowerCase().includes(keySearchText.value.toLowerCase())
  )
})

const activeKeyCount = computed(() => {
  return apiKeys.value.filter(key => key.isActive).length
})

const inactiveKeyCount = computed(() => {
  return apiKeys.value.filter(key => !key.isActive).length
})

// 方法
const selectGroup = (group: UserConfigGroupDto) => {
  aiConfigStore.setCurrentGroup(group)
}

const editGroup = (group: UserConfigGroupDto) => {
  editingGroup.value = { ...group }
  showCreateGroupDialog.value = true
}

const deleteGroup = async (group: UserConfigGroupDto) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分组"${group.groupName}"吗？此操作将同时删除该分组下的所有API密钥。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await aiConfigStore.deleteConfigGroup(group.id!)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const refreshApiKeys = () => {
  if (currentGroup.value?.id) {
    aiConfigStore.fetchApiKeys(currentGroup.value.id, true)
  }
}

const handleGroupSaved = () => {
  showCreateGroupDialog.value = false
  editingGroup.value = null
  ElMessage.success('保存成功')
}

const handleBatchUploadSuccess = (result: any) => {
  showBatchUploadDialog.value = false
  ElMessage.success(`批量添加完成：成功 ${result.successCount} 个，失败 ${result.failedCount} 个`)
}

const handleCompatibleKeyGenerated = () => {
  showCompatibleKeyDialog.value = false
  ElMessage.success('兼容密钥生成成功')
}

// 测试API密钥
const testApiKey = async (apiKey: ApiKeyDto) => {
  if (!apiKey.id) return

  testingKeys.value.push(apiKey.id)
  try {
    const result = await aiConfigStore.testApiKey(apiKey.id)

    if (result.valid) {
      ElMessage.success(`测试成功：${result.message}（响应时间：${result.responseTime}ms）`)
    } else {
      ElMessage.error(`测试失败：${result.message}`)
    }
  } catch (error) {
    ElMessage.error('测试失败，请重试')
  } finally {
    testingKeys.value = testingKeys.value.filter(id => id !== apiKey.id)
  }
}

// 切换API密钥状态
const toggleApiKeyStatus = async (apiKey: ApiKeyDto) => {
  if (!apiKey.id) return

  try {
    const newStatus = !apiKey.isActive
    await aiConfigStore.updateApiKey(apiKey.id, { isActive: newStatus })
    ElMessage.success(`密钥已${newStatus ? '启用' : '禁用'}`)
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  }
}

// 删除API密钥
const deleteApiKey = async (apiKey: ApiKeyDto) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除密钥"${apiKey.keyName || apiKey.maskedApiKey}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await aiConfigStore.deleteApiKey(apiKey.id!)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 生命周期
onMounted(() => {
  aiConfigStore.fetchConfigGroups()
})

onUnmounted(() => {
  // 清理错误状态
  aiConfigStore.clearError()
  // 重置测试状态
  testingKeys.value = []
})
</script>

<style scoped>
.ai-key-management {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.layout-container {
  flex: 1;
  display: flex;
  gap: 1.5rem;
  overflow: hidden;
}

.sidebar {
  width: 320px;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.sidebar-header h3 {
  margin: 0;
  color: white;
  font-weight: 600;
}

.group-search {
  margin-bottom: 1rem;
}

.group-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.group-item {
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s var(--lg-ease-smooth);
  border-left: 3px solid transparent;
}

.group-item:hover {
  transform: translateX(4px);
}

.group-item.active {
  border-left-color: #3b82f6;
  background: var(--lg-glass-bg-active);
}

.group-info {
  margin-bottom: 0.75rem;
}

.group-name {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
  font-size: 1rem;
}

.group-provider {
  display: flex;
  align-items: center;
}

.group-actions {
  display: flex;
  gap: 0.5rem;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.group-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  overflow: hidden;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.group-title h2 {
  margin: 0;
  color: white;
  font-weight: 700;
  font-size: 1.5rem;
}

.group-stats {
  display: flex;
  gap: 1rem;
}

.stat-item {
  padding: 1rem;
  text-align: center;
  min-width: 100px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.action-bar {
  display: flex;
  gap: 1rem;
  padding: 0 1.5rem;
}

.api-keys-section {
  flex: 1;
  padding: 1.5rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  margin: 0;
  color: white;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.search-input {
  width: 250px;
}

.masked-key {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .layout-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    max-height: 300px;
  }

  .group-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .stat-item {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .ai-key-management {
    padding: 0.5rem;
  }

  .layout-container {
    gap: 1rem;
  }

  .sidebar,
  .api-keys-section {
    padding: 1rem;
  }

  .group-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .action-bar {
    flex-wrap: wrap;
    padding: 0 1rem;
  }

  .search-input {
    width: 200px;
  }
}

/* Element Plus 组件样式覆盖 */
.liquid-glass-container :deep(.el-table) {
  background: transparent;
  color: white;
}

.liquid-glass-container :deep(.el-table th.el-table__cell) {
  background: var(--lg-glass-bg);
  color: white;
  border-bottom: 1px solid var(--lg-glass-border);
}

.liquid-glass-container :deep(.el-table td.el-table__cell) {
  border-bottom: 1px solid var(--lg-glass-border);
}

.liquid-glass-container :deep(.el-table__row) {
  background: transparent;
  color: white;
}

.liquid-glass-container :deep(.el-table__row:hover) {
  background: var(--lg-glass-bg-hover) !important;
}

.liquid-glass-container :deep(.el-input__wrapper) {
  background: var(--lg-glass-bg);
  border: 1px solid var(--lg-glass-border);
  border-radius: var(--lg-radius-2xl);
  backdrop-filter: blur(20px) saturate(180%);
  box-shadow: var(--lg-glass-shadow);
}

.liquid-glass-container :deep(.el-input__wrapper:hover) {
  background: var(--lg-glass-bg-hover);
  box-shadow: var(--lg-glass-shadow-hover);
}

.liquid-glass-container :deep(.el-input__wrapper.is-focus) {
  background: var(--lg-glass-bg-hover);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--lg-glass-shadow);
}

.liquid-glass-container :deep(.el-input__inner) {
  color: #0f0303;
}

.liquid-glass-container :deep(.el-input__inner::placeholder) {
  color: #0f0303;
}

.liquid-glass-container :deep(.el-button) {
  border-radius: var(--lg-radius-2xl);
  backdrop-filter: blur(20px) saturate(180%);
  transition: all 0.3s var(--lg-ease-smooth);
}

.liquid-glass-container :deep(.el-button:hover) {
  transform: translateY(-2px);
  box-shadow: var(--lg-glass-shadow-hover);
}

.liquid-glass-container :deep(.el-tag) {
  border-radius: var(--lg-radius-full);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--lg-glass-border);
}

.liquid-glass-container :deep(.el-empty__description) {
  color: rgba(255, 255, 255, 0.8);
}
</style>
