<template>
  <div class="liquid-glass-container request-log-viewer">
    <!-- 搜索和筛选区域 -->
    <div class="search-section lg-glass lg-fade-in">
      <div class="search-header">
        <h3>请求日志查询</h3>
      </div>

      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="lg-input"
          />
        </el-form-item>

        <el-form-item label="提供商">
          <el-select
            v-model="searchForm.provider"
            placeholder="全部"
            clearable
            class="lg-input"
            style="width: 150px"
          >
            <el-option label="OpenAI" value="openai" />
            <el-option label="Claude" value="claude" />
            <el-option label="Gemini" value="gemini" />
            <el-option label="Azure" value="azure" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="全部"
            clearable
            class="lg-input"
            style="width: 120px"
          >
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>

        <el-form-item label="请求类型">
          <el-select
            v-model="searchForm.requestType"
            placeholder="全部"
            clearable
            class="lg-input"
            style="width: 120px"
          >
            <el-option label="流式" value="stream" />
            <el-option label="普通" value="normal" />
          </el-select>
        </el-form-item>

        <el-form-item label="模型">
          <el-input
            v-model="searchForm.modelName"
            placeholder="模型名称"
            clearable
            class="lg-input"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" class="lg-button" @click="handleSearch">搜索</el-button>
          <el-button class="lg-button" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 快捷筛选 -->
      <div class="quick-filters">
        <el-button-group>
          <el-button class="lg-button" @click="showFailedLogs">失败请求</el-button>
          <el-button class="lg-button" @click="showSlowLogs">慢请求</el-button>
          <el-button class="lg-button" @click="showRecentLogs">最近1小时</el-button>
          <el-button class="lg-button" @click="showHighRetryLogs">高重试</el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section lg-slide-up">
      <div class="stats-grid">
        <div class="stat-card lg-glass">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalRequests || 0 }}</div>
            <div class="stat-label">总请求数</div>
          </div>
        </div>
        <div class="stat-card lg-glass">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.successRate || 0 }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
        <div class="stat-card lg-glass">
          <div class="stat-icon">⚡</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.avgDuration || 0 }}ms</div>
            <div class="stat-label">平均耗时</div>
          </div>
        </div>
        <div class="stat-card lg-glass">
          <div class="stat-icon">❌</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.failedRequests || 0 }}</div>
            <div class="stat-label">失败请求</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志表格 -->
    <div class="table-section lg-glass lg-slide-up">
      <div class="table-header">
        <h3>请求日志</h3>
        <div class="table-actions">
          <el-input
            v-model="tableSearchText"
            placeholder="搜索日志..."
            prefix-icon="Search"
            clearable
            class="lg-input search-input"
          />
          <el-button class="lg-button" @click="refreshLogs" :loading="loading.logs">
            刷新
          </el-button>
          <el-button class="lg-button" @click="exportLogs">
            导出
          </el-button>
        </div>
      </div>

      <el-table
        :data="filteredLogs"
        :loading="loading.logs"
        stripe
        class="lg-table"
        @row-click="showLogDetail"
        height="400"
      >
        <el-table-column prop="requestId" label="请求ID" width="200" fixed="left">
          <template #default="{ row }">
            <code class="request-id">{{ row.requestId.slice(0, 8) }}...</code>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="请求时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="requestType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.requestType === 'stream' ? 'success' : 'info'"
              class="lg-tag"
            >
              {{ row.requestType === 'stream' ? '流式' : '普通' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="provider" label="提供商" width="100">
          <template #default="{ row }">
            <el-tag class="lg-tag">{{ row.provider }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="modelName" label="模型" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              class="lg-tag"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="durationMs" label="耗时" width="100">
          <template #default="{ row }">
            <span :class="getDurationClass(row.durationMs)">
              {{ row.durationMs || 0 }}ms
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="retryCount" label="重试次数" width="100" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              size="small"
              text
              class="lg-button"
              @click.stop="showLogDetail(row)"
            >
              详情
            </el-button>
            <el-button
              size="small"
              text
              type="danger"
              class="lg-button"
              @click.stop="deleteLog(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <LogDetailDialog
      v-model="showDetailDialog"
      :log="selectedLog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRequestLogStore } from '@/store/modules/requestLog'
import { formatDateTime } from '@/utils/formatTime'
import LogDetailDialog from './components/LogDetailDialog.vue'
import type { RequestLogQueryRequest, OpenAiRequestLogResponse } from '@/types/requestLog'

// 引入Liquid Glass样式
import '@/style/liquid-glass.css'

const requestLogStore = useRequestLogStore()

// 响应式数据
const showDetailDialog = ref(false)
const selectedLog = ref<OpenAiRequestLogResponse | null>(null)
const tableSearchText = ref('')

const searchForm = reactive({
  dateRange: [] as string[],
  provider: '',
  status: '',
  requestType: '',
  modelName: ''
})

// 计算属性
const logs = computed(() => requestLogStore.logs)
const loading = computed(() => requestLogStore.loading)
const pagination = computed(() => requestLogStore.pagination)

const filteredLogs = computed(() => {
  if (!Array.isArray(logs.value)) return []
  if (!tableSearchText.value) return logs.value
  return logs.value.filter(log =>
    log.requestId.toLowerCase().includes(tableSearchText.value.toLowerCase()) ||
    log.modelName.toLowerCase().includes(tableSearchText.value.toLowerCase()) ||
    log.provider.toLowerCase().includes(tableSearchText.value.toLowerCase())
  )
})

const stats = computed(() => ({
  totalRequests: requestLogStore.stats?.totalRequests || requestLogStore.pagination.total || 0,
  successRate: requestLogStore.stats?.successRate || requestLogStore.successRate || 0,
  avgDuration: requestLogStore.stats?.avgDuration || requestLogStore.avgResponseTime || 0,
  failedRequests: requestLogStore.stats?.failedRequests || requestLogStore.failedLogsCount || 0
}))

// 方法
const buildQuery = (): RequestLogQueryRequest => {
  const query: RequestLogQueryRequest = {
    page: pagination.value.page,
    size: pagination.value.size
  }

  if (searchForm.dateRange.length === 2) {
    query.startTime = searchForm.dateRange[0]
    query.endTime = searchForm.dateRange[1]
  }

  if (searchForm.provider) query.provider = searchForm.provider
  if (searchForm.status) query.status = searchForm.status
  if (searchForm.requestType) query.requestType = searchForm.requestType
  if (searchForm.modelName) query.modelName = searchForm.modelName

  return query
}

const fetchLogs = async () => {
  try {
    const query = buildQuery()
    await requestLogStore.fetchLogs(query)
  } catch (error) {
    ElMessage.error('获取日志失败')
  }
}

const handleSearch = () => {
  fetchLogs()
}

const handleReset = () => {
  searchForm.dateRange = []
  searchForm.provider = ''
  searchForm.status = ''
  searchForm.requestType = ''
  searchForm.modelName = ''
  fetchLogs()
}

const showFailedLogs = async () => {
  try {
    await requestLogStore.fetchFailedLogs(1, 20)
  } catch (error) {
    ElMessage.error('获取失败日志失败')
  }
}

const showSlowLogs = async () => {
  try {
    await requestLogStore.fetchSlowLogs(1, 20, 5000)
    ElMessage.info(`找到 ${requestLogStore.pagination.total} 个慢请求（>5秒）`)
  } catch (error) {
    ElMessage.error('获取慢请求失败')
  }
}

const showRecentLogs = () => {
  const now = new Date()
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
  searchForm.dateRange = [
    oneHourAgo.toISOString().slice(0, 19).replace('T', ' '),
    now.toISOString().slice(0, 19).replace('T', ' ')
  ]
  fetchLogs()
}

const showHighRetryLogs = async () => {
  try {
    // 使用高级搜索API查询重试次数大于2的请求
    const query = buildQuery()
    query.retryCountRange = { min: 3, max: 999 }
    await requestLogStore.fetchLogs(query)
    ElMessage.info(`找到 ${requestLogStore.pagination.total} 个高重试请求（>2次）`)
  } catch (error) {
    ElMessage.error('获取高重试请求失败')
  }
}

const refreshLogs = () => {
  fetchLogs()
}

const exportLogs = () => {
  // 导出功能实现
  ElMessage.info('导出功能开发中...')
}

const showLogDetail = async (log: OpenAiRequestLogResponse) => {
  try {
    await requestLogStore.fetchLogDetail(log.requestId)
    selectedLog.value = requestLogStore.currentLog
    showDetailDialog.value = true
  } catch (error) {
    ElMessage.error('获取日志详情失败')
  }
}

const deleteLog = async (log: OpenAiRequestLogResponse) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除请求ID为 ${log.requestId} 的日志吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await requestLogStore.deleteLog(log.id)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = (size: number) => {
  requestLogStore.updateFilters({ size, page: 1 })
  fetchLogs()
}

const handleCurrentChange = (page: number) => {
  requestLogStore.updateFilters({ page })
  fetchLogs()
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'success': return 'success'
    case 'failed': return 'danger'
    default: return 'warning'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'success': return '成功'
    case 'failed': return '失败'
    default: return '未知'
  }
}

const getDurationClass = (duration: number | null) => {
  if (!duration) return ''
  if (duration > 10000) return 'duration-very-slow'
  if (duration > 5000) return 'duration-slow'
  return ''
}

// 生命周期
onMounted(async () => {
  await fetchLogs()
  // 获取全局统计数据
  try {
    await requestLogStore.fetchStats()
  } catch (error) {
    console.warn('获取统计数据失败:', error)
  }
})

onUnmounted(() => {
  // 清理错误状态
  requestLogStore.clearError()
  // 重置过滤条件
  requestLogStore.resetFilters()
})
</script>

<style scoped>
.request-log-viewer {
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.search-section {
  padding: 1.5rem;
}

.search-header {
  margin-bottom: 1.5rem;
}

.search-header h3 {
  margin: 0;
  color: white;
  font-weight: 600;
  font-size: 1.25rem;
}

.search-form {
  margin-bottom: 1rem;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 1rem;
}

.search-form :deep(.el-form-item__label) {
  color: #0f0303;
  font-weight: 500;
}

.quick-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.stats-section {
  animation-delay: 0.1s;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s var(--lg-ease-smooth);
}

.stat-card:hover {
  transform: translateY(-4px) scale(1.02);
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.table-section {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  animation-delay: 0.2s;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.table-header h3 {
  margin: 0;
  color: white;
  font-weight: 600;
  font-size: 1.25rem;
}

.table-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  width: 250px;
}

.request-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #3b82f6;
}

.duration-slow {
  color: #f59e0b;
  font-weight: 600;
}

.duration-very-slow {
  color: #ef4444;
  font-weight: 600;
}

.pagination-wrapper {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .table-actions {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .request-log-viewer {
    padding: 0.5rem;
    gap: 1rem;
  }

  .search-section,
  .table-section {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .search-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .search-form :deep(.el-form-item) {
    margin-bottom: 0;
  }

  .quick-filters {
    justify-content: center;
  }

  .table-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .search-input {
    width: 100%;
  }
}

/* Element Plus 组件样式覆盖 */
.liquid-glass-container :deep(.el-table) {
  background: transparent;
  color: white;
}

.liquid-glass-container :deep(.el-table th.el-table__cell) {
  background: var(--lg-glass-bg);
  color: white;
  border-bottom: 1px solid var(--lg-glass-border);
}

.liquid-glass-container :deep(.el-table td.el-table__cell) {
  border-bottom: 1px solid var(--lg-glass-border);
}

.liquid-glass-container :deep(.el-table__row) {
  background: transparent;
  color: white;
}

.liquid-glass-container :deep(.el-table__row:hover) {
  background: var(--lg-glass-bg-hover) !important;
}

.liquid-glass-container :deep(.el-input__wrapper) {
  background: var(--lg-glass-bg);
  border: 1px solid var(--lg-glass-border);
  border-radius: var(--lg-radius-2xl);
  backdrop-filter: blur(20px) saturate(180%);
  box-shadow: var(--lg-glass-shadow);
}

.liquid-glass-container :deep(.el-input__wrapper:hover) {
  background: var(--lg-glass-bg-hover);
  box-shadow: var(--lg-glass-shadow-hover);
}

.liquid-glass-container :deep(.el-input__wrapper.is-focus) {
  background: var(--lg-glass-bg-hover);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--lg-glass-shadow);
}

.liquid-glass-container :deep(.el-input__inner) {
  color: #0f0303;
}

.liquid-glass-container :deep(.el-input__inner::placeholder) {
  color: #0f0303;
}

.liquid-glass-container :deep(.el-select .el-input__wrapper) {
  background: var(--lg-glass-bg);
}

.liquid-glass-container :deep(.el-select .el-input__inner) {
  color: #0f0303;
}

/* 下拉选项样式 */
.liquid-glass-container :deep(.el-select-dropdown) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--lg-glass-border);
  border-radius: var(--lg-radius-2xl);
  box-shadow: var(--lg-glass-shadow);
}

.liquid-glass-container :deep(.el-select-dropdown .el-select-dropdown__item) {
  color: #333;
  background: transparent;
}

.liquid-glass-container :deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background: rgba(59, 130, 246, 0.1);
}

.liquid-glass-container :deep(.el-select-dropdown .el-select-dropdown__item.is-selected) {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.liquid-glass-container :deep(.el-button) {
  border-radius: var(--lg-radius-2xl);
  backdrop-filter: blur(20px) saturate(180%);
  transition: all 0.3s var(--lg-ease-smooth);
}

.liquid-glass-container :deep(.el-button:hover) {
  transform: translateY(-2px);
  box-shadow: var(--lg-glass-shadow-hover);
}

.liquid-glass-container :deep(.el-tag) {
  border-radius: var(--lg-radius-full);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--lg-glass-border);
}

.liquid-glass-container :deep(.el-date-editor) {
  background: var(--lg-glass-bg);
  border: 1px solid var(--lg-glass-border);
  border-radius: var(--lg-radius-2xl);
  backdrop-filter: blur(20px) saturate(180%);
}

.liquid-glass-container :deep(.el-date-editor .el-input__inner) {
  color: #0f0303;
}

.liquid-glass-container :deep(.el-pagination) {
  color: white;
}

.liquid-glass-container :deep(.el-pagination .el-pager li) {
  background: var(--lg-glass-bg);
  border: 1px solid var(--lg-glass-border);
  border-radius: var(--lg-radius-2xl);
  color: white;
  margin: 0 0.125rem;
}

.liquid-glass-container :deep(.el-pagination .el-pager li:hover) {
  background: var(--lg-glass-bg-hover);
  transform: translateY(-2px);
}

.liquid-glass-container :deep(.el-pagination .el-pager li.is-active) {
  background: #3b82f6;
  color: white;
}

.liquid-glass-container :deep(.el-pagination .btn-prev),
.liquid-glass-container :deep(.el-pagination .btn-next) {
  background: var(--lg-glass-bg);
  border: 1px solid var(--lg-glass-border);
  border-radius: var(--lg-radius-2xl);
  color: white;
}

.liquid-glass-container :deep(.el-pagination .btn-prev:hover),
.liquid-glass-container :deep(.el-pagination .btn-next:hover) {
  background: var(--lg-glass-bg-hover);
  transform: translateY(-2px);
}
</style>
