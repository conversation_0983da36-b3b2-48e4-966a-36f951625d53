import { http } from "@/utils/http";
import type {
  UserConfigGroupDto,
  ApiKeyDto,
  BatchAddApiKeyRequest,
  BatchAddApiKeyResult,
  CompatibleApiKeyDto,
  CreateCompatibleKeyRequest,
  ApiKeyTestResult,
  ApiKeyUpdateRequest,
  ApiKeyStatsDto,
  ApiResponse,
  PageResult
} from "@/types/aiConfig";

// AI配置API服务类
export class AiConfigApi {
  // 配置分组管理相关API

  /**
   * 获取用户配置分组列表
   */
  static async getUserConfigGroups(): Promise<UserConfigGroupDto[]> {
    const response = await http.request<ApiResponse<UserConfigGroupDto[]>>(
      "get",
      "/api/ai/config/config-groups"
    );
    return response.data || [];
  }

  /**
   * 创建或更新配置分组
   */
  static async createOrUpdateUserConfigGroup(
    config: UserConfigGroupDto
  ): Promise<UserConfigGroupDto> {
    const response = await http.request<ApiResponse<UserConfigGroupDto>>(
      "put",
      "/api/ai/config/config-groups",
      {
        data: config
      }
    );
    return response.data;
  }

  /**
   * 删除配置分组
   */
  static async deleteUserConfigGroup(groupId: number): Promise<boolean> {
    const response = await http.request<ApiResponse<boolean>>(
      "delete",
      `/api/ai/config/config-groups/${groupId}`
    );
    return response.success;
  }

  /**
   * 获取配置分组详情
   */
  static async getUserConfigGroupById(
    groupId: number
  ): Promise<UserConfigGroupDto> {
    const response = await http.get<ApiResponse<UserConfigGroupDto>>(
      `/api/ai/config/config-groups/${groupId}`
    );
    return response.data;
  }

  // API密钥管理相关API

  /**
   * 根据配置分组ID获取API密钥列表
   */
  static async getUserApiKeysByConfigGroupId(
    configGroupId: number
  ): Promise<ApiKeyDto[]> {
    const response = await http.get<ApiResponse<ApiKeyDto[]>>(
      `/api/ai/config/api-keys/config-group/${configGroupId}`
    );
    return response.data || [];
  }

  /**
   * 批量添加API密钥
   */
  static async batchAddApiKeys(
    request: BatchAddApiKeyRequest
  ): Promise<BatchAddApiKeyResult> {
    const response = await http.post<ApiResponse<BatchAddApiKeyResult>>(
      "/api/ai/config/api-keys/batch",
      request
    );
    return response.data;
  }

  /**
   * 获取API密钥统计信息
   */
  static async getApiKeyStats(configGroupId?: number): Promise<ApiKeyStatsDto> {
    const url = configGroupId
      ? `/api/ai/config/api-keys/stats?configGroupId=${configGroupId}`
      : "/api/ai/config/api-keys/stats";
    const response = await http.get<ApiResponse<ApiKeyStatsDto>>(url);
    return response.data;
  }

  /**
   * 测试API密钥有效性
   */
  static async testApiKey(keyId: number): Promise<ApiKeyTestResult> {
    const response = await http.post<ApiResponse<ApiKeyTestResult>>(
      `/api/ai/config/api-keys/${keyId}/test`
    );
    return response.data;
  }

  /**
   * 更新API密钥信息
   */
  static async updateApiKey(
    keyId: number,
    updateData: ApiKeyUpdateRequest
  ): Promise<ApiKeyDto> {
    const response = await http.put<ApiResponse<ApiKeyDto>>(
      `/api/ai/config/api-keys/${keyId}`,
      updateData
    );
    return response.data;
  }

  /**
   * 删除API密钥
   */
  static async deleteApiKey(keyId: number): Promise<boolean> {
    const response = await http.delete<ApiResponse<boolean>>(
      `/api/ai/config/api-keys/${keyId}`
    );
    return response.success;
  }

  /**
   * 获取单个API密钥详情
   */
  static async getApiKeyById(keyId: number): Promise<ApiKeyDto> {
    const response = await http.get<ApiResponse<ApiKeyDto>>(
      `/api/ai/config/api-keys/${keyId}`
    );
    return response.data;
  }

  // 兼容密钥管理相关API

  /**
   * 获取兼容密钥列表
   */
  static async getCompatibleKeys(): Promise<CompatibleApiKeyDto[]> {
    const response = await http.get<ApiResponse<CompatibleApiKeyDto[]>>(
      "/api/ai/config/compatible-keys"
    );
    return response.data || [];
  }

  /**
   * 生成兼容密钥
   */
  static async generateCompatibleKey(
    request: CreateCompatibleKeyRequest
  ): Promise<CompatibleApiKeyDto> {
    const response = await http.post<ApiResponse<CompatibleApiKeyDto>>(
      "/api/ai/config/compatible-keys",
      request
    );
    return response.data;
  }

  /**
   * 删除兼容密钥
   */
  static async deleteCompatibleKey(keyId: number): Promise<boolean> {
    const response = await http.delete<ApiResponse<boolean>>(
      `/api/ai/config/compatible-keys/${keyId}`
    );
    return response.success;
  }

  /**
   * 验证兼容密钥
   */
  static async validateCompatibleKey(key: string): Promise<boolean> {
    const response = await http.post<ApiResponse<boolean>>(
      "/api/ai/config/compatible-keys/validate",
      { key }
    );
    return response.data;
  }

  /**
   * 更新兼容密钥
   */
  static async updateCompatibleKey(
    keyId: number,
    updateData: Partial<CompatibleApiKeyDto>
  ): Promise<CompatibleApiKeyDto> {
    const response = await http.put<ApiResponse<CompatibleApiKeyDto>>(
      `/api/ai/config/compatible-keys/${keyId}`,
      updateData
    );
    return response.data;
  }

  // 工具方法

  /**
   * 获取支持的提供商列表
   */
  static async getSupportedProviders(): Promise<string[]> {
    const response = await http.get<ApiResponse<string[]>>(
      "/api/ai/config/providers"
    );
    return response.data || [];
  }

  /**
   * 根据提供商获取支持的模型列表
   */
  static async getModelsByProvider(provider: string): Promise<string[]> {
    const response = await http.get<ApiResponse<string[]>>(
      `/api/ai/config/providers/${provider}/models`
    );
    return response.data || [];
  }

  /**
   * 批量测试API密钥
   */
  static async batchTestApiKeys(keyIds: number[]): Promise<ApiKeyTestResult[]> {
    const response = await http.post<ApiResponse<ApiKeyTestResult[]>>(
      "/api/ai/config/api-keys/batch-test",
      { keyIds }
    );
    return response.data || [];
  }

  /**
   * 导出配置分组
   */
  static async exportConfigGroup(groupId: number): Promise<Blob> {
    const response = await http.get(
      `/api/ai/config/config-groups/${groupId}/export`,
      {
        responseType: "blob"
      }
    );
    return response;
  }

  /**
   * 导入配置分组
   */
  static async importConfigGroup(file: File): Promise<UserConfigGroupDto> {
    const formData = new FormData();
    formData.append("file", file);
    const response = await http.post<ApiResponse<UserConfigGroupDto>>(
      "/api/ai/config/config-groups/import",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      }
    );
    return response.data;
  }
}
