import { http } from "@/utils/http";
import type {
  RequestLogQueryRequest,
  OpenAiRequestLogResponse,
  RequestLogDetail,
  LogStatsData,
  ProviderStats,
  TimeRangeStats,
  ErrorTypeStats,
  ModelUsageStats,
  LogAnalysisResult,
  LogExportOptions,
  PageResult,
  ApiResponse
} from "@/types/requestLog";

// 请求日志API服务类
export class RequestLogApi {
  // 基础查询API

  /**
   * 分页查询请求日志
   */
  static async getOpenaiRequestLogsPage(
    query: RequestLogQueryRequest
  ): Promise<PageResult<OpenAiRequestLogResponse>> {
    const response = await http.request<ApiResponse<{
      list: OpenAiRequestLogResponse[];
      pageResult: {
        total: number;
        pageNum: number;
        pageSize: number;
        pages: number;
      };
    }>>(
      "get",
      "/api/logs/openai-requests/page",
      {
        params: query
      }
    );

    // 转换后端响应格式为前端期望格式
    return {
      data: response.data.list || [],
      total: response.data.pageResult?.total || 0,
      page: response.data.pageResult?.pageNum || 1,
      size: response.data.pageResult?.pageSize || 20,
      totalPages: response.data.pageResult?.pages || 0
    };
  }

  /**
   * 查询失败的请求日志
   */
  static async getFailedOpenaiRequestLogs(
    page: number,
    size: number,
    userId?: number
  ): Promise<PageResult<OpenAiRequestLogResponse>> {
    const params = { page, size, ...(userId && { userId }) };
    const response = await http.request<PageResult<OpenAiRequestLogResponse>>(
      "get",
      "/api/logs/openai-requests/failed",
      {
        params
      }
    );
    return response;
  }

  /**
   * 查询慢请求日志
   */
  static async getSlowOpenaiRequestLogs(
    page: number,
    size: number,
    minDuration: number = 5000,
    userId?: number
  ): Promise<PageResult<OpenAiRequestLogResponse>> {
    const params = { page, size, minDuration, ...(userId && { userId }) };
    const response = await http.request<PageResult<OpenAiRequestLogResponse>>(
      "get",
      "/api/logs/openai-requests/slow",
      {
        params
      }
    );
    return response;
  }

  /**
   * 根据提供商查询请求日志
   */
  static async getOpenaiRequestLogsByProvider(
    provider: string,
    page: number,
    size: number
  ): Promise<PageResult<OpenAiRequestLogResponse>> {
    const response = await http.request<PageResult<OpenAiRequestLogResponse>>(
      "get",
      `/api/logs/openai-requests/provider/${provider}`,
      {
        params: { page, size }
      }
    );
    return response;
  }

  /**
   * 获取请求日志详情
   */
  static async getRequestLogDetail(requestId: string): Promise<RequestLogDetail> {
    const response = await http.request<ApiResponse<RequestLogDetail>>(
      "get",
      `/api/logs/openai-requests/${requestId}`
    );
    return response.data;
  }

  /**
   * 根据用户ID查询请求日志
   */
  static async getRequestLogsByUserId(
    userId: number,
    page: number,
    size: number
  ): Promise<PageResult<OpenAiRequestLogResponse>> {
    const response = await http.request<PageResult<OpenAiRequestLogResponse>>(
      "get",
      `/api/logs/openai-requests/user/${userId}`,
      {
        params: { page, size }
      }
    );
    return response;
  }

  // 删除操作API

  /**
   * 删除单个请求日志
   */
  static async deleteOpenaiRequestLog(id: number): Promise<boolean> {
    const response = await http.request<any>(
      "delete",
      `/api/logs/openai-requests/${id}`
    );
    return response.status === 200;
  }

  /**
   * 批量删除请求日志
   */
  static async batchDeleteRequestLogs(ids: number[]): Promise<boolean> {
    const response = await http.request<any>(
      "delete",
      "/api/logs/openai-requests/batch",
      {
        data: { ids }
      }
    );
    return response.status === 200;
  }

  /**
   * 清理过期日志
   */
  static async cleanupExpiredLogs(daysToKeep: number): Promise<number> {
    const response = await http.request<ApiResponse<number>>(
      "post",
      "/api/logs/openai-requests/cleanup",
      {
        data: { daysToKeep }
      }
    );
    return response.data;
  }

  // 统计分析API

  /**
   * 获取日志统计数据
   */
  static async getLogStats(
    startTime?: string,
    endTime?: string,
    provider?: string
  ): Promise<LogStatsData> {
    const params = { startTime, endTime, provider };
    const response = await http.request<ApiResponse<LogStatsData>>(
      "get",
      "/api/logs/openai-requests/page",
      {
        params
      }
    )
    return response.data;
  }

  /**
   * 获取提供商统计
   */
  static async getProviderStats(
    startTime?: string,
    endTime?: string
  ): Promise<ProviderStats[]> {
    const params = { startTime, endTime };
    const response = await http.request<ApiResponse<ProviderStats[]>>(
      "get",
      "/api/logs/openai-requests/stats/providers",
      {
        params
      }
    );
    return response.data || [];
  }

  /**
   * 获取时间段统计
   */
  static async getTimeRangeStats(
    startTime: string,
    endTime: string,
    interval: "hour" | "day" | "week" = "hour"
  ): Promise<TimeRangeStats[]> {
    const params = { startTime, endTime, interval };
    const response = await http.request<ApiResponse<TimeRangeStats[]>>(
      "get",
      "/api/logs/openai-requests/stats/time-range",
      {
        params
      }
    );
    return response.data || [];
  }

  /**
   * 获取错误类型统计
   */
  static async getErrorTypeStats(
    startTime?: string,
    endTime?: string
  ): Promise<ErrorTypeStats[]> {
    const params = { startTime, endTime };
    const response = await http.request<ApiResponse<ErrorTypeStats[]>>(
      "get",
      "/api/logs/openai-requests/stats/errors",
      {
        params
      }
    );
    return response.data || [];
  }

  /**
   * 获取模型使用统计
   */
  static async getModelUsageStats(
    startTime?: string,
    endTime?: string,
    provider?: string
  ): Promise<ModelUsageStats[]> {
    const params = { startTime, endTime, provider };
    const response = await http.request<ApiResponse<ModelUsageStats[]>>(
      "get",
      "/api/logs/openai-requests/stats/models",
      {
        params
      }
    );
    return response.data || [];
  }

  /**
   * 获取完整的日志分析结果
   */
  static async getLogAnalysis(
    startTime?: string,
    endTime?: string,
    provider?: string
  ): Promise<LogAnalysisResult> {
    const params = { startTime, endTime, provider };
    const response = await http.request<ApiResponse<LogAnalysisResult>>(
      "get",
      "/api/logs/openai-requests/analysis",
      {
        params
      }
    );
    return response.data;
  }

  // 导出功能API

  /**
   * 导出请求日志
   */
  static async exportRequestLogs(options: LogExportOptions): Promise<Blob> {
    const response = await http.request<Blob>(
      "post",
      "/api/logs/openai-requests/export",
      {
        data: options,
        responseType: "blob"
      }
    );
    return response;
  }

  /**
   * 获取导出任务状态
   */
  static async getExportTaskStatus(taskId: string): Promise<{
    status: "pending" | "processing" | "completed" | "failed";
    progress: number;
    downloadUrl?: string;
    errorMessage?: string;
  }> {
    const response = await http.request<ApiResponse<any>>(
      "get",
      `/api/logs/openai-requests/export/status/${taskId}`
    );
    return response.data;
  }

  // 搜索和过滤API

  /**
   * 高级搜索请求日志
   */
  static async advancedSearchLogs(searchParams: {
    keyword?: string;
    requestId?: string;
    errorMessage?: string;
    statusCodes?: number[];
    durationRange?: { min: number; max: number };
    retryCountRange?: { min: number; max: number };
    page: number;
    size: number;
  }): Promise<PageResult<OpenAiRequestLogResponse>> {
    const response = await http.request<PageResult<OpenAiRequestLogResponse>>(
      "post",
      "/api/logs/openai-requests/search",
      {
        data: searchParams
      }
    );
    return response;
  }

  /**
   * 获取慢请求日志
   */
  static async getSlowRequestLogs(
    minDuration: number,
    page: number,
    size: number
  ): Promise<PageResult<OpenAiRequestLogResponse>> {
    const params = { minDuration, page, size };
    const response = await http.request<PageResult<OpenAiRequestLogResponse>>(
      "get",
      "/api/logs/openai-requests/slow",
      {
        params
      }
    );
    return response;
  }

  /**
   * 获取重试次数较多的请求日志
   */
  static async getHighRetryLogs(
    minRetryCount: number,
    page: number,
    size: number
  ): Promise<PageResult<OpenAiRequestLogResponse>> {
    const params = { minRetryCount, page, size };
    const response = await http.request<PageResult<OpenAiRequestLogResponse>>(
      "get",
      "/api/logs/openai-requests/high-retry",
      {
        params
      }
    );
    return response;
  }

  // 实时监控API

  /**
   * 获取实时日志流
   */
  static async getRealtimeLogs(): Promise<EventSource> {
    return new EventSource("/api/logs/openai-requests/realtime");
  }

  /**
   * 获取实时统计数据
   */
  static async getRealtimeStats(): Promise<{
    currentRequests: number;
    requestsPerMinute: number;
    errorRate: number;
    avgResponseTime: number;
  }> {
    const response = await http.request<ApiResponse<any>>(
      "get",
      "/api/logs/openai-requests/realtime/stats"
    );
    return response.data;
  }
}
