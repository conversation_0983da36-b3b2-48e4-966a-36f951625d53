# ISO 格式时间修复说明

## 修复概述

根据用户要求，将 AI 大模型请求记录日志页面的时间格式修改为标准 ISO 8601 格式，确保与后端 API 的兼容性。

## 修复内容

### 1. 日期选择器格式修改

**修改文件**: `src/views/aimodel-aggregator/RequestLogViewer.vue`

```vue
<!-- 修复前 -->
<el-date-picker
  v-model="searchForm.dateRange"
  type="datetimerange"
  format="YYYY-MM-DD HH:mm:ss"
  value-format="YYYY-MM-DD HH:mm:ss"
/>

<!-- 修复后 -->
<el-date-picker
  v-model="searchForm.dateRange"
  type="datetimerange"
  format="YYYY-MM-DD HH:mm:ss"
  value-format="YYYY-MM-DDTHH:mm:ss.sssZ"
/>
```

**说明**: 
- 保持显示格式为用户友好的 `YYYY-MM-DD HH:mm:ss`
- 值格式改为标准 ISO 8601 格式 `YYYY-MM-DDTHH:mm:ss.sssZ`

### 2. 时间格式转换函数

**新增函数**: `formatDateToISO`

```typescript
const formatDateToISO = (dateStr: string): string => {
  if (!dateStr) return dateStr
  
  // 如果已经是 ISO 格式，直接返回
  if (dateStr.includes('T') && (dateStr.includes('Z') || dateStr.includes('+'))) {
    return dateStr
  }
  
  // 如果是 YYYY-MM-DD HH:mm:ss 格式，转换为 ISO 格式
  if (dateStr.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
    return new Date(dateStr.replace(' ', 'T') + 'Z').toISOString()
  }
  
  // 尝试解析并转换为 ISO 格式
  try {
    return new Date(dateStr).toISOString()
  } catch {
    return dateStr
  }
}
```

**功能**:
- 智能检测输入时间格式
- 自动转换为标准 ISO 8601 格式
- 容错处理，避免转换失败

### 3. 查询参数构建优化

**修改函数**: `buildQuery`

```typescript
const buildQuery = (): RequestLogQueryRequest => {
  const query: RequestLogQueryRequest = {
    page: pagination.value.page,
    size: pagination.value.size
  }

  if (searchForm.dateRange.length === 2) {
    // 确保时间参数使用标准 ISO 格式
    query.startTime = formatDateToISO(searchForm.dateRange[0])
    query.endTime = formatDateToISO(searchForm.dateRange[1])
  }

  // ... 其他参数
  return query
}
```

### 4. 快捷时间选择修复

**修改函数**: `showRecentLogs`

```typescript
// 修复前
const showRecentLogs = () => {
  const now = new Date()
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
  searchForm.dateRange = [
    oneHourAgo.toISOString().slice(0, 19).replace('T', ' '),
    now.toISOString().slice(0, 19).replace('T', ' ')
  ]
  fetchLogs()
}

// 修复后
const showRecentLogs = () => {
  const now = new Date()
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
  searchForm.dateRange = [
    oneHourAgo.toISOString(),
    now.toISOString()
  ]
  fetchLogs()
}
```

## 时间格式对比

| 场景 | 修复前格式 | 修复后格式 |
|------|------------|------------|
| 日期选择器输出 | `2024-01-15 10:30:00` | `2024-01-15T10:30:00.000Z` |
| 快捷时间选择 | `2024-01-15 10:30:00` | `2024-01-15T10:30:00.000Z` |
| API 请求参数 | `2024-01-15 10:30:00` | `2024-01-15T10:30:00.000Z` |

## 兼容性说明

### 向后兼容
- 转换函数支持多种输入格式
- 自动检测并转换旧格式
- 错误处理确保系统稳定性

### 标准合规
- 符合 ISO 8601 国际标准
- 包含时区信息 (UTC)
- 支持毫秒精度

## 测试用例

### 输入格式测试
```typescript
// 测试用例
formatDateToISO('2024-01-15 10:30:00')        // → '2024-01-15T10:30:00.000Z'
formatDateToISO('2024-01-15T10:30:00.000Z')   // → '2024-01-15T10:30:00.000Z'
formatDateToISO('2024-01-15T10:30:00+08:00')  // → '2024-01-15T10:30:00+08:00'
formatDateToISO('')                           // → ''
formatDateToISO('invalid')                    // → 'invalid'
```

## 修复效果

### ✅ 标准化
- 所有时间参数使用 ISO 8601 标准格式
- 统一的时间处理逻辑

### ✅ 兼容性
- 支持多种输入格式的自动转换
- 向后兼容现有数据

### ✅ 可靠性
- 错误处理机制完善
- TypeScript 类型检查通过

### ✅ 用户体验
- 界面显示格式保持用户友好
- 后台处理使用标准格式

## 相关文件

- `src/views/aimodel-aggregator/RequestLogViewer.vue` - 主要修改文件
- `docs/OpenAiRequestLogController-API文档.md` - API 接口文档参考

## 修复时间

修复完成时间：2025-01-04

**正在使用的模型：Claude Sonnet 4**
