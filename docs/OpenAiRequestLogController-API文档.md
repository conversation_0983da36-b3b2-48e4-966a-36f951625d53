# OpenAI请求日志控制器 API 文档

## 概述
OpenAiRequestLogController 提供OpenAI请求日志的查询和管理功能，包括分页查询、条件筛选、详情查看和删除操作。

**基础路径**: `/api/logs/openai-requests`

---

## 1. 分页查询OpenAI请求日志

### 接口信息
- **URL**: `GET /api/logs/openai-requests/page`
- **描述**: 根据查询条件分页获取OpenAI请求日志列表
- **权限**: 需要登录

### 请求参数 (Query Parameters)
```json
{
  "page": 1,                    // 页码，从1开始，默认1
  "size": 20,                   // 每页大小，默认20，最大100
  "userId": 123,                // 用户ID（可选）
  "provider": "openai",         // AI提供商（可选）
  "status": "成功",             // 请求状态（可选）
  "requestType": "stream",      // 请求类型（可选）
  "modelName": "gpt-4",         // 模型名称（可选，支持模糊查询）
  "startTime": "2024-01-01 00:00:00",  // 开始时间（可选）
  "endTime": "2024-01-31 23:59:59",    // 结束时间（可选）
  "minDuration": 1000,          // 最小耗时毫秒（可选）
  "maxDuration": 10000,         // 最大耗时毫秒（可选）
  "onlyFailed": false,          // 是否只查询失败请求（可选）
  "onlySlow": false,            // 是否只查询慢请求（可选）
  "sortBy": "created_at",       // 排序字段（可选）
  "sortOrder": "desc"           // 排序方向（可选）
}
```

### 响应格式
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "list": [
      {
        "id": 1,
        "requestId": "req_123456789",
        "userId": 123,
        "startTime": "2024-01-15 10:30:00",
        "endTime": "2024-01-15 10:30:05",
        "status": "成功",
        "statusCode": 200,
        "requestType": "stream",
        "durationMs": 5000,
        "retryCount": 0,
        "groupName": "default",
        "provider": "openai",
        "maskedApiKey": "sk-****1234",
        "errorMessage": null,
        "actualBaseUrl": "https://api.openai.com",
        "pathSuffix": "/v1/chat/completions",
        "modelName": "gpt-4",
        "extraInfo": "额外信息",
        "createdAt": "2024-01-15 10:30:00"
      }
    ],
    "pageResult": {
      "total": 100,
      "pageNum": 1,
      "pageSize": 20,
      "pages": 5
    }
  },
  "time": "2024-01-15T10:30:00.000Z"
}
```

---

## 2. 查询单个日志详情

### 接口信息
- **URL**: `GET /api/logs/openai-requests/{requestId}`
- **描述**: 根据请求ID获取单个日志的详细信息
- **权限**: 需要登录

### 路径参数
- `requestId` (String): 请求ID

### 响应格式
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "id": 1,
    "requestId": "req_123456789",
    "userId": 123,
    "startTime": "2024-01-15 10:30:00",
    "endTime": "2024-01-15 10:30:05",
    "status": "成功",
    "statusCode": 200,
    "requestType": "stream",
    "durationMs": 5000,
    "retryCount": 0,
    "groupName": "default",
    "provider": "openai",
    "maskedApiKey": "sk-****1234",
    "errorMessage": null,
    "actualBaseUrl": "https://api.openai.com",
    "pathSuffix": "/v1/chat/completions",
    "modelName": "gpt-4",
    "extraInfo": "额外信息",
    "createdAt": "2024-01-15 10:30:00"
  },
  "time": "2024-01-15T10:30:00.000Z"
}
```

### 错误响应
```json
{
  "code": 404,
  "message": "请求日志不存在",
  "success": false,
  "data": null,
  "time": "2024-01-15T10:30:00.000Z"
}
```

---

## 3. 查询失败的请求日志

### 接口信息
- **URL**: `GET /api/logs/openai-requests/failed`
- **描述**: 分页查询失败的OpenAI请求日志
- **权限**: 需要登录

### 请求参数 (Query Parameters)
```json
{
  "page": 1,        // 页码，默认1
  "size": 20,       // 每页大小，默认20
  "userId": 123     // 用户ID（可选）
}
```

### 响应格式
与分页查询接口相同，但只返回失败的请求日志。

---

## 4. 查询慢请求日志

### 接口信息
- **URL**: `GET /api/logs/openai-requests/slow`
- **描述**: 分页查询耗时超过指定时间的慢请求日志
- **权限**: 需要登录

### 请求参数 (Query Parameters)
```json
{
  "page": 1,            // 页码，默认1
  "size": 20,           // 每页大小，默认20
  "minDuration": 5000,  // 最小耗时毫秒，默认5000ms
  "userId": 123         // 用户ID（可选）
}
```

### 响应格式
与分页查询接口相同，但只返回慢请求日志。

---

## 5. 根据提供商查询日志

### 接口信息
- **URL**: `GET /api/logs/openai-requests/provider/{provider}`
- **描述**: 根据AI提供商分页查询请求日志
- **权限**: 需要登录

### 路径参数
- `provider` (String): AI提供商名称

### 请求参数 (Query Parameters)
```json
{
  "page": 1,    // 页码，默认1
  "size": 20    // 每页大小，默认20
}
```

### 响应格式
与分页查询接口相同。

---

## 6. 根据模型名称查询日志

### 接口信息
- **URL**: `GET /api/logs/openai-requests/model/{modelName}`
- **描述**: 根据模型名称分页查询请求日志
- **权限**: 需要登录

### 路径参数
- `modelName` (String): 模型名称

### 请求参数 (Query Parameters)
```json
{
  "page": 1,    // 页码，默认1
  "size": 20    // 每页大小，默认20
}
```

### 响应格式
与分页查询接口相同。

---

## 7. 删除请求日志

### 接口信息
- **URL**: `DELETE /api/logs/openai-requests/{id}`
- **描述**: 根据ID删除指定的请求日志
- **权限**: 需要管理员权限

### 路径参数
- `id` (Long): 日志ID

### 响应格式
```json
{
  "code": 200,
  "message": "删除成功",
  "success": true,
  "data": "删除成功",
  "time": "2024-01-15T10:30:00.000Z"
}
```

### 错误响应
```json
{
  "code": 404,
  "message": "日志不存在",
  "success": false,
  "data": null,
  "time": "2024-01-15T10:30:00.000Z"
}
```

---

## 数据模型说明

### OpenAiRequestLogResponse 字段说明
| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 主键ID |
| requestId | String | 请求ID，用于追踪 |
| userId | Long | 用户ID |
| startTime | String | 请求开始时间 (yyyy-MM-dd HH:mm:ss) |
| endTime | String | 请求结束时间 (yyyy-MM-dd HH:mm:ss) |
| status | String | 请求状态（成功/失败） |
| statusCode | Integer | HTTP状态码 |
| requestType | String | 请求类型（stream/non-stream） |
| durationMs | Long | 耗时（毫秒） |
| retryCount | Integer | 重试次数 |
| groupName | String | 配置分组名称 |
| provider | String | AI提供商 |
| maskedApiKey | String | 脱敏的API密钥 |
| errorMessage | String | 错误信息（失败时） |
| actualBaseUrl | String | 实际请求的BaseURL |
| pathSuffix | String | 请求路径后缀 |
| modelName | String | 模型名称 |
| extraInfo | String | 额外信息 |
| createdAt | String | 创建时间 (yyyy-MM-dd HH:mm:ss) |

### 状态码说明
- `200`: 操作成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### 请求状态枚举
- `成功`: 请求成功完成
- `失败`: 请求失败

### 请求类型枚举
- `stream`: 流式请求
- `non-stream`: 非流式请求

---

## 使用示例

### 1. 查询最近的失败日志
```bash
curl -X GET "http://localhost:8080/api/logs/openai-requests/failed?page=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 2. 查询特定用户的慢请求
```bash
curl -X GET "http://localhost:8080/api/logs/openai-requests/slow?userId=123&minDuration=3000" \
  -H "Authorization: Bearer your-token"
```

### 3. 查询OpenAI提供商的日志
```bash
curl -X GET "http://localhost:8080/api/logs/openai-requests/provider/openai?page=1&size=20" \
  -H "Authorization: Bearer your-token"
```

### 4. 删除指定日志
```bash
curl -X DELETE "http://localhost:8080/api/logs/openai-requests/123" \
  -H "Authorization: Bearer your-token"
```
